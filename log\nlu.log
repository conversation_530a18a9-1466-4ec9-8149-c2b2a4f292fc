INFO:     Started server process [5742]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8009 (Press CTRL+C to quit)
2025-05-10 09:38:48.706 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,1,428,292, cost: 0.4152364730834961
INFO:     127.0.0.1:51696 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:38:52.630 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,2,1,354, cost: 0.02768564224243164
INFO:     127.0.0.1:51710 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:38:56.102 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,319,150, cost: 0.028208255767822266
INFO:     127.0.0.1:51712 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:38:58.010 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,135,88, cost: 0.026793956756591797
INFO:     127.0.0.1:60240 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:01.489 INFO [pid-5742] @chatnlu_infer.py:99 top5：56,3,123,121,89, cost: 0.026636600494384766
2025-05-10 09:39:06.493 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:39:06.494 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:39:06.495 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.005651473999023
INFO:     127.0.0.1:60246 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:06.521 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,56,1,428, cost: 0.024509906768798828
INFO:     127.0.0.1:60260 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:09.708 INFO [pid-5742] @chatnlu_infer.py:99 top5：56,121,63,123,138, cost: 0.026505470275878906
2025-05-10 09:39:10.709 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Color": "蓝色"}', 'name': 'Set_Env_Light_Color'}, 'id': 'call_uoiag78a0gxdijvbcx9vy9zm', 'type': 'function'}]
2025-05-10 09:39:10.710 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0018253326416016
2025-05-10 09:39:10.710 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置氛围灯颜色-COLOR:蓝色
INFO:     127.0.0.1:53978 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:13.755 INFO [pid-5742] @chatnlu_infer.py:99 top5：304,3,96,50,343, cost: 0.02570819854736328
2025-05-10 09:39:18.904 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:39:18.904 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:39:18.904 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.1495020389556885
INFO:     127.0.0.1:53984 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:18.931 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,50,2,428,1, cost: 0.024990320205688477
INFO:     127.0.0.1:53780 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:20.101 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,103,312,51, cost: 0.026297569274902344
2025-05-10 09:39:21.213 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "周杰伦"}', 'name': 'Search_Music'}, 'id': 'call_cgbadwri5xdw92m54lts2l20', 'type': 'function'}]
2025-05-10 09:39:21.214 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1130785942077637
2025-05-10 09:39:21.215 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:周杰伦
INFO:     127.0.0.1:53782 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:21.964 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,293, cost: 0.02553081512451172
INFO:     127.0.0.1:53790 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:24.663 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,150, cost: 0.025507450103759766
INFO:     127.0.0.1:53802 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:31.216 INFO [pid-5742] @chatnlu_infer.py:99 top5：51,49,386,142,2, cost: 0.029284954071044922
2025-05-10 09:39:32.377 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Person": "郭德纲", "Type": "相声"}', 'name': 'Search_Radio'}, 'id': 'call_pktc00iwnuw09rp70hqwdk0z', 'type': 'function'}]
2025-05-10 09:39:32.378 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1616148948669434
2025-05-10 09:39:32.378 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-主播名称:郭德纲,电台类型:相声
INFO:     127.0.0.1:60040 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:32.478 INFO [pid-5742] @chatnlu_infer.py:99 top5：64,366,176,16,255, cost: 0.025669097900390625
2025-05-10 09:39:33.293 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Env_Light'}, 'id': 'call_uasy5276wpbn8rsl4adygest', 'type': 'function'}]
2025-05-10 09:39:33.294 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8160381317138672
2025-05-10 09:39:33.294 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开氛围灯-无
INFO:     127.0.0.1:60046 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:34.262 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,64,176,50,366, cost: 0.02521657943725586
2025-05-10 09:39:34.990 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_0ph8a6jj3e43radxzacrl8qf', 'type': 'function'}]
2025-05-10 09:39:34.991 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7282416820526123
2025-05-10 09:39:34.991 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:60060 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:37.513 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,431,51,103, cost: 0.02636432647705078
2025-05-10 09:39:38.574 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "成都"}', 'name': 'Search_Music'}, 'id': 'call_uu0afnjde1pa5anmbvf0qgu1', 'type': 'function'}]
2025-05-10 09:39:38.575 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.061138391494751
2025-05-10 09:39:38.575 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:成都
INFO:     127.0.0.1:60072 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:39.552 INFO [pid-5742] @chatnlu_infer.py:99 top5：120,402,26,354,194, cost: 0.026215076446533203
2025-05-10 09:39:40.486 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"location": "那边"}', 'name': 'Query_Timely_Weather'}, 'id': 'call_ve9zceoklorns16pizjmwpmy', 'type': 'function'}]
2025-05-10 09:39:40.487 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9344432353973389
2025-05-10 09:39:40.487 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：实时查询天气-city:那边
INFO:     127.0.0.1:52218 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:40.888 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,346,137, cost: 0.02659130096435547
2025-05-10 09:39:41.617 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_dvroslmgbnpz0j7onqbrpbq6', 'type': 'function'}]
2025-05-10 09:39:41.617 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7287211418151855
2025-05-10 09:39:41.618 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:52230 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:46.136 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,432,223,428, cost: 0.025304079055786133
2025-05-10 09:39:47.032 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_f000d77spy66levk7m1qq2jf', 'type': 'function'}]
2025-05-10 09:39:47.032 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8959236145019531
2025-05-10 09:39:47.032 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:52232 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:51.090 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,3,59, cost: 0.026604652404785156
2025-05-10 09:39:52.237 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "麦当劳"}', 'name': 'Go_POI'}, 'id': 'call_q39gjzg0e7aiyvtwn2g11e1c', 'type': 'function'}]
2025-05-10 09:39:52.238 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.147371768951416
2025-05-10 09:39:52.238 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:麦当劳
INFO:     127.0.0.1:54768 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:53.015 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,102, cost: 0.02670431137084961
2025-05-10 09:39:58.160 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:39:58.160 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:39:58.160 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.144556760787964
INFO:     127.0.0.1:54782 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:58.197 INFO [pid-5742] @chatnlu_infer.py:99 top5：88,356,327,162,14, cost: 0.035193681716918945
2025-05-10 09:39:59.032 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Air_Condition'}, 'id': 'call_xk5kxn7s4ejdxf168hqqjiy1', 'type': 'function'}]
2025-05-10 09:39:59.033 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8352329730987549
2025-05-10 09:39:59.033 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开空调-无
INFO:     127.0.0.1:54798 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:39:59.906 INFO [pid-5742] @chatnlu_infer.py:99 top5：15,3,136,120,4, cost: 0.024187803268432617
2025-05-10 09:40:04.960 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:40:04.963 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:40:04.963 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.057194471359253
INFO:     127.0.0.1:42856 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:04.989 INFO [pid-5742] @chatnlu_infer.py:99 top5：209,174,201,106,194, cost: 0.024414777755737305
2025-05-10 09:40:06.211 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "location": "", "sunscreen": "今天需要涂防晒吗"}', 'name': 'Query_UV_Level'}, 'id': 'call_5ljgh5w54me3uthqdh3ms6n2', 'type': 'function'}]
2025-05-10 09:40:06.211 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2215209007263184
2025-05-10 09:40:06.211 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查询紫外线-date:今天,SUNSCREEN:今天需要涂防晒吗
INFO:     127.0.0.1:42868 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:07.145 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,293, cost: 0.025523662567138672
INFO:     127.0.0.1:42870 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:10.970 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,59,3, cost: 0.02437567710876465
2025-05-10 09:40:12.156 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "茶馆", "Target": "当前位置"}', 'name': 'Go_POI'}, 'id': 'call_t1cin116131wyx1d8xj6fhk8', 'type': 'function'}]
2025-05-10 09:40:12.157 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1860768795013428
2025-05-10 09:40:12.157 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:茶馆,landmark:当前位置
INFO:     127.0.0.1:39378 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:12.929 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,346, cost: 0.025144577026367188
INFO:     127.0.0.1:39388 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:16.358 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,2,102,150, cost: 0.028995513916015625
2025-05-10 09:40:21.462 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:40:21.463 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:40:21.463 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.104738712310791
INFO:     127.0.0.1:39394 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:22.782 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,3,59, cost: 0.02525925636291504
2025-05-10 09:40:23.779 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "故宫"}', 'name': 'Go_POI'}, 'id': 'call_y468elxfoabxvz0c24ubtjyw', 'type': 'function'}]
2025-05-10 09:40:23.780 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9979815483093262
2025-05-10 09:40:23.780 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:故宫
INFO:     127.0.0.1:35218 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:24.212 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,150, cost: 0.026154756546020508
INFO:     127.0.0.1:35230 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:26.204 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,59,3, cost: 0.02422809600830078
2025-05-10 09:40:27.120 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "成都"}', 'name': 'Go_POI'}, 'id': 'call_nug0qatv3emy2u3sz4ka5xug', 'type': 'function'}]
2025-05-10 09:40:27.120 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9164552688598633
2025-05-10 09:40:27.120 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:成都
INFO:     127.0.0.1:35232 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:27.466 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,428,1, cost: 0.025990724563598633
INFO:     127.0.0.1:35240 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:32.094 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,312,363, cost: 0.024935245513916016
2025-05-10 09:40:33.283 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "汪峰", "Name": "怒放的生命"}', 'name': 'Search_Music'}, 'id': 'call_lfhkykljvpgdhv0iwr4d301s', 'type': 'function'}]
2025-05-10 09:40:33.283 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1890265941619873
2025-05-10 09:40:33.283 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:汪峰,歌曲:怒放的生命
INFO:     127.0.0.1:47662 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:33.381 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,428,102,137, cost: 0.023328542709350586
2025-05-10 09:40:38.018 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:40:38.019 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:40:38.019 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.637892484664917
INFO:     127.0.0.1:47672 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:38.044 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,3,59, cost: 0.02394247055053711
2025-05-10 09:40:39.068 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "长城"}', 'name': 'Go_POI'}, 'id': 'call_bea5p88pu7lwdfn9u1umm7v3', 'type': 'function'}]
2025-05-10 09:40:39.068 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0240833759307861
2025-05-10 09:40:39.068 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:长城
INFO:     127.0.0.1:47680 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:39.163 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,1,319,26, cost: 0.02387857437133789
INFO:     127.0.0.1:34088 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:42.751 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,402,154,192,304, cost: 0.02402520179748535
2025-05-10 09:40:43.743 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "下周", "location": "重庆市"}', 'name': 'Query_Weather'}, 'id': 'call_0h5dlbl05ybx6g8cx9jfzun8', 'type': 'function'}]
2025-05-10 09:40:43.743 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.991793155670166
2025-05-10 09:40:43.743 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:下周,city:重庆市
INFO:     127.0.0.1:34098 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:43.805 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,2,428,1, cost: 0.024651765823364258
INFO:     127.0.0.1:34106 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:47.293 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,431,363, cost: 0.02595233917236328
2025-05-10 09:40:48.352 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "张杰"}', 'name': 'Search_Music'}, 'id': 'call_0s7qsirzoydaagmwy44yj0hy', 'type': 'function'}]
2025-05-10 09:40:48.352 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0590898990631104
2025-05-10 09:40:48.352 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:张杰
INFO:     127.0.0.1:34110 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:48.457 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,51,85, cost: 0.02915167808532715
2025-05-10 09:40:53.560 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:40:53.560 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:40:53.560 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.103177070617676
INFO:     127.0.0.1:48082 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:53.585 INFO [pid-5742] @chatnlu_infer.py:99 top5：51,2,386,49,142, cost: 0.02408885955810547
2025-05-10 09:40:54.787 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "郭麒麟的电台", "Person": "郭麒麟"}', 'name': 'Search_Radio'}, 'id': 'call_e7ub3t3r1e3crm3ziygdmjd1', 'type': 'function'}]
2025-05-10 09:40:54.788 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.202775478363037
2025-05-10 09:40:54.788 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-节目名称:郭麒麟的电台,主播名称:郭麒麟
INFO:     127.0.0.1:48098 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:54.887 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,2,1,150, cost: 0.02621769905090332
INFO:     127.0.0.1:48102 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:58.494 INFO [pid-5742] @chatnlu_infer.py:99 top5：150,149,3,85,323, cost: 0.02396368980407715
2025-05-10 09:40:59.493 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Person": "唐纳德·特朗普", "Today": "1"}', 'name': 'Search_News'}, 'id': 'call_3dn29tafmj8jsk3k4q8hv8ph', 'type': 'function'}]
2025-05-10 09:40:59.493 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9989066123962402
2025-05-10 09:40:59.493 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：新闻搜索-新闻人物:唐纳德·特朗普,实时:1
INFO:     127.0.0.1:32812 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:40:59.642 INFO [pid-5742] @chatnlu_infer.py:99 top5：199,3,293,155,13, cost: 0.023096323013305664
2025-05-10 09:41:04.745 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:41:04.745 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:41:04.745 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.10309624671936
INFO:     127.0.0.1:32824 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:04.781 INFO [pid-5742] @chatnlu_infer.py:99 top5：199,92,155,243,13, cost: 0.02575206756591797
2025-05-10 09:41:05.649 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_DashCam'}, 'id': 'call_cswr27fhelwhsvrd31rw00z9', 'type': 'function'}]
2025-05-10 09:41:05.650 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8688392639160156
2025-05-10 09:41:05.650 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开行车记录仪-无
INFO:     127.0.0.1:32838 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:06.597 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,133,184,52,2, cost: 0.025701045989990234
INFO:     127.0.0.1:32854 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:10.946 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,40,130,196,274, cost: 0.024164438247680664
2025-05-10 09:41:12.120 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"One_Level": "特色功能", "Two_Level": "智能驾驶辅助系统"}', 'name': 'Open_System_Config'}, 'id': 'call_viusybnl9riudlyiylxcoeu6', 'type': 'function'}]
2025-05-10 09:41:12.120 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1740717887878418
2025-05-10 09:41:12.120 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开系统设置-一级菜单设置项:特色功能,二级菜单设置项:智能驾驶辅助系统
INFO:     127.0.0.1:44422 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:12.220 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,125,196, cost: 0.024121761322021484
INFO:     127.0.0.1:44428 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:17.824 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,2,137, cost: 0.02458477020263672
2025-05-10 09:41:18.911 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "瑞幸"}', 'name': 'Go_POI'}, 'id': 'call_40uosg5qpt1wcjzi7c2b91es', 'type': 'function'}]
2025-05-10 09:41:18.911 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0863792896270752
2025-05-10 09:41:18.911 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:瑞幸
INFO:     127.0.0.1:41536 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:19.014 INFO [pid-5742] @chatnlu_infer.py:99 top5：214,3,383,324,313, cost: 0.023857831954956055
2025-05-10 09:41:22.927 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:41:22.930 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:41:22.930 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.9165897369384766
INFO:     127.0.0.1:41540 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:25.168 INFO [pid-5742] @chatnlu_infer.py:99 top5：214,324,31,411,313, cost: 0.02487015724182129
2025-05-10 09:41:26.176 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Yellow": "12345"}', 'name': 'Call_Yellow_Page'}, 'id': 'call_rph6zdryokkn97hukb39v7tv', 'type': 'function'}]
2025-05-10 09:41:26.176 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.008439302444458
2025-05-10 09:41:26.177 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打黄页-黄页电话:12345
INFO:     127.0.0.1:41552 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:26.230 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,1,72, cost: 0.024315357208251953
2025-05-10 09:41:31.008 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:41:31.009 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:41:31.010 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.779491901397705
INFO:     127.0.0.1:41554 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:35.361 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,363,431, cost: 0.027412891387939453
2025-05-10 09:41:36.441 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "黄石"}', 'name': 'Search_Radio'}, 'id': 'call_ar29jgbrgic4d1twowfwy89f', 'type': 'function'}]
2025-05-10 09:41:36.442 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0800929069519043
2025-05-10 09:41:36.442 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-节目名称:黄石
INFO:     127.0.0.1:46784 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:36.537 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,319,1,360,428, cost: 0.023427486419677734
2025-05-10 09:41:39.732 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:41:39.735 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:41:39.735 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.1979899406433105
INFO:     127.0.0.1:46786 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:42.018 INFO [pid-5742] @chatnlu_infer.py:99 top5：214,324,31,3,172, cost: 0.025951147079467773
2025-05-10 09:41:43.128 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Emergency": "119"}', 'name': 'Call_Emergency'}, 'id': 'call_seriog4axhtfm9dn7ektqqmb', 'type': 'function'}]
2025-05-10 09:41:43.129 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1112432479858398
2025-05-10 09:41:43.129 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打紧急电话-紧急电话:119
INFO:     127.0.0.1:60382 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:43.256 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,432,354,1,428, cost: 0.025896072387695312
INFO:     127.0.0.1:60384 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:49.264 INFO [pid-5742] @chatnlu_infer.py:99 top5：432,3,292,435,1, cost: 0.027604103088378906
2025-05-10 09:41:50.442 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Tire": "1"}', 'name': 'Check_Car_Condition'}, 'id': 'call_nzhnf69rdn9wmhr6r3i17kpm', 'type': 'function'}]
2025-05-10 09:41:50.443 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1784346103668213
2025-05-10 09:41:50.443 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：车况检查-tire:1
INFO:     127.0.0.1:58490 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:50.549 INFO [pid-5742] @chatnlu_infer.py:99 top5：15,136,120,4,167, cost: 0.030725955963134766
2025-05-10 09:41:52.390 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:41:52.392 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:41:52.393 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.8429524898529053
INFO:     127.0.0.1:58504 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:54.918 INFO [pid-5742] @chatnlu_infer.py:99 top5：15,136,167,4,162, cost: 0.026417016983032227
2025-05-10 09:41:55.993 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Number": "24"}', 'name': 'Set_Air_Condition_Temperature'}, 'id': 'call_ej97ee3pagk5shxkpi0bsrhy', 'type': 'function'}]
2025-05-10 09:41:55.994 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0758168697357178
2025-05-10 09:41:55.994 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置空调温度-number:24
INFO:     127.0.0.1:58512 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:41:56.051 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,213,432,292, cost: 0.026083946228027344
2025-05-10 09:41:56.980 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_qgtqf1w5wldwykhwqvphnc3z', 'type': 'function'}]
2025-05-10 09:41:56.980 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9290452003479004
2025-05-10 09:41:56.981 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:58518 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:00.794 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,213,292,346, cost: 0.029508590698242188
2025-05-10 09:42:01.944 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "明天", "nutrient": "钙"}', 'name': 'Ask_Diet'}, 'id': 'call_pz2gvibpl6vvy9ggv5rhushh', 'type': 'function'}]
2025-05-10 09:42:01.945 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1504459381103516
2025-05-10 09:42:01.945 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：Ask_Diet-无
INFO:     127.0.0.1:48014 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:04.006 INFO [pid-5742] @chatnlu_infer.py:99 top5：51,72,3,49,142, cost: 0.027379512786865234
2025-05-10 09:42:09.150 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:42:09.150 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:42:09.150 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.143548011779785
INFO:     127.0.0.1:48020 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:09.178 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,150,72, cost: 0.02591395378112793
2025-05-10 09:42:10.397 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "BBC World Service"}', 'name': 'Search_Radio'}, 'id': 'call_icpdemolqmlp0p5pwqp2hp6u', 'type': 'function'}]
2025-05-10 09:42:10.397 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2190508842468262
2025-05-10 09:42:10.397 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-节目名称:BBC World Service
INFO:     127.0.0.1:40452 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:10.453 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,51,85, cost: 0.024581432342529297
INFO:     127.0.0.1:40458 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:16.001 INFO [pid-5742] @chatnlu_infer.py:99 top5：51,2,49,142,125, cost: 0.024977445602416992
2025-05-10 09:42:17.086 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "金庸武侠世界", "Type": "电视剧"}', 'name': 'Search_Radio'}, 'id': 'call_lk74kdbe49b9oc562i50t6wa', 'type': 'function'}]
2025-05-10 09:42:17.086 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0850083827972412
2025-05-10 09:42:17.086 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-节目名称:金庸武侠世界,电台类型:电视剧
INFO:     127.0.0.1:40460 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:17.185 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,1,319,346, cost: 0.0251162052154541
INFO:     127.0.0.1:40472 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:19.384 INFO [pid-5742] @chatnlu_infer.py:99 top5：85,49,72,142,51, cost: 0.02670764923095703
2025-05-10 09:42:20.414 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "北京", "Type": "广播"}', 'name': 'Search_Station'}, 'id': 'call_uxmxxgq4wtzu4rsb52jwws4x', 'type': 'function'}]
2025-05-10 09:42:20.415 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0301165580749512
2025-05-10 09:42:20.415 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：广播搜索-节目名称:北京,节目类型:广播
INFO:     127.0.0.1:37080 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:20.845 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,221,51,103, cost: 0.0255277156829834
2025-05-10 09:42:23.407 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:42:23.409 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:42:23.410 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.564387559890747
INFO:     127.0.0.1:37094 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:25.544 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,363,312,103,51, cost: 0.02660346031188965
2025-05-10 09:42:26.616 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"genre": "蓝调"}', 'name': 'Search_Music'}, 'id': 'call_urkp01gg2k6ncuiq06jd0phu', 'type': 'function'}]
2025-05-10 09:42:26.617 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0717973709106445
2025-05-10 09:42:26.617 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲流派:蓝调
INFO:     127.0.0.1:37104 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:26.717 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,103,317, cost: 0.026197433471679688
2025-05-10 09:42:27.916 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"charts": "经典怀旧榜", "Language": "英语"}', 'name': 'Search_Music'}, 'id': 'call_yehoe58dnqawcd0nz1flkzx3', 'type': 'function'}]
2025-05-10 09:42:27.917 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1992990970611572
2025-05-10 09:42:27.917 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-榜单:经典怀旧榜,歌曲语言:英语
INFO:     127.0.0.1:37108 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:34.745 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,103,317,312, cost: 0.02733635902404785
2025-05-10 09:42:35.780 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "My Heart Will Go On"}', 'name': 'Search_Music'}, 'id': 'call_m3lkkwmlv4ummy03b0owb2oj', 'type': 'function'}]
2025-05-10 09:42:35.781 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0350241661071777
2025-05-10 09:42:35.781 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:My Heart Will Go On
INFO:     127.0.0.1:55830 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:35.886 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,150,2,51,354, cost: 0.025503158569335938
INFO:     127.0.0.1:55838 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:39.316 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,59, cost: 0.028684377670288086
2025-05-10 09:42:40.246 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "复旦大学"}', 'name': 'Go_POI'}, 'id': 'call_qpmdk6qxh3z6ysqy2xllgodm', 'type': 'function'}]
2025-05-10 09:42:40.247 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9309265613555908
2025-05-10 09:42:40.247 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:复旦大学
INFO:     127.0.0.1:35296 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:40.537 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,430,150, cost: 0.025371551513671875
INFO:     127.0.0.1:35304 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:49.886 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,363,51, cost: 0.02517867088317871
2025-05-10 09:42:50.858 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Mood": "治愈"}', 'name': 'Search_Music'}, 'id': 'call_o3gwpevd54k3jyncks8plkca', 'type': 'function'}]
2025-05-10 09:42:50.859 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9728312492370605
2025-05-10 09:42:50.859 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲心情:治愈
INFO:     127.0.0.1:51772 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:51.180 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,8,44, cost: 0.027974605560302734
INFO:     127.0.0.1:51784 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:57.152 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,51,312,363, cost: 0.025792598724365234
2025-05-10 09:42:58.273 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"charts": "歌曲飙升榜"}', 'name': 'Search_Music'}, 'id': 'call_1ejrw3m7mdk3njzf22yo2wjb', 'type': 'function'}]
2025-05-10 09:42:58.274 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1215460300445557
2025-05-10 09:42:58.274 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-榜单:歌曲飙升榜
INFO:     127.0.0.1:51796 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:42:58.553 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,150,2,85,149, cost: 0.025231122970581055
2025-05-10 09:43:00.173 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:43:00.175 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:43:00.176 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.6234261989593506
INFO:     127.0.0.1:56630 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:07.035 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,51,363,312, cost: 0.025272846221923828
2025-05-10 09:43:08.187 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "张韶涵", "Singer2": "范玮琪"}', 'name': 'Search_Music'}, 'id': 'call_o7yitp26bsvwnxro5kj9s6vh', 'type': 'function'}]
2025-05-10 09:43:08.188 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1525611877441406
2025-05-10 09:43:08.188 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:张韶涵,歌手2:范玮琪
INFO:     127.0.0.1:56638 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:08.291 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,8,1,208, cost: 0.025930404663085938
INFO:     127.0.0.1:51426 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:14.350 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,178,103,317, cost: 0.026175498962402344
2025-05-10 09:43:15.276 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "懦夫"}', 'name': 'Search_Music'}, 'id': 'call_j9p7vv5x2pf9m57nm9unmddw', 'type': 'function'}]
2025-05-10 09:43:15.277 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9261717796325684
2025-05-10 09:43:15.277 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:懦夫
INFO:     127.0.0.1:51438 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:15.451 INFO [pid-5742] @chatnlu_infer.py:99 top5：298,262,3,312,398, cost: 0.026149511337280273
2025-05-10 09:43:18.490 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:43:18.492 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:43:18.493 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.0420894622802734
INFO:     127.0.0.1:51448 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:19.152 INFO [pid-5742] @chatnlu_infer.py:99 top5：298,312,262,398,198, cost: 0.02628612518310547
2025-05-10 09:43:20.098 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Media_Source": "歌曲"}', 'name': 'Open_Media_Collection'}, 'id': 'call_f3zhqofawqhcjyhtj2hnzora', 'type': 'function'}]
2025-05-10 09:43:20.099 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9467949867248535
2025-05-10 09:43:20.099 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开多媒体收藏-媒体源:歌曲
INFO:     127.0.0.1:49136 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:20.484 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,428,102,137, cost: 0.026215314865112305
2025-05-10 09:43:21.361 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:43:21.363 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:43:21.364 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8796143531799316
INFO:     127.0.0.1:49142 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:23.500 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,22, cost: 0.026210784912109375
2025-05-10 09:43:24.480 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "峨眉山山脚下停车场"}', 'name': 'Go_POI'}, 'id': 'call_ep4db97d8g7myk0fgkyx5f4j', 'type': 'function'}]
2025-05-10 09:43:24.481 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9800646305084229
2025-05-10 09:43:24.481 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:峨眉山山脚下停车场
INFO:     127.0.0.1:49150 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:24.704 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,2,125, cost: 0.028759479522705078
2025-05-10 09:43:29.851 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:43:29.851 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:43:29.851 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.1468665599823
INFO:     127.0.0.1:49160 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:31.139 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,8, cost: 0.02609539031982422
2025-05-10 09:43:32.430 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "杭州", "POI": "星巴克", "Target": "西湖"}', 'name': 'Go_POI'}, 'id': 'call_ol99ruvkz4zbou4fexhynqv8', 'type': 'function'}]
2025-05-10 09:43:32.431 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2910187244415283
2025-05-10 09:43:32.431 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:杭州,POI:星巴克,landmark:西湖
INFO:     127.0.0.1:36926 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:32.482 INFO [pid-5742] @chatnlu_infer.py:99 top5：135,410,35,435,3, cost: 0.024688720703125
2025-05-10 09:43:33.261 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Trunk'}, 'id': 'call_f50kt19almmgjjyp8gqhdzoo', 'type': 'function'}]
2025-05-10 09:43:33.262 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7790608406066895
2025-05-10 09:43:33.262 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开后备箱-无
INFO:     127.0.0.1:36936 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:36.977 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,135,1,428,410, cost: 0.026215314865112305
2025-05-10 09:43:37.730 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_vrp4086fcowzirw8xp4neltc', 'type': 'function'}]
2025-05-10 09:43:37.731 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7540488243103027
2025-05-10 09:43:37.731 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:36952 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:40.196 INFO [pid-5742] @chatnlu_infer.py:99 top5：120,402,26,354,3, cost: 0.025969266891479492
2025-05-10 09:43:41.230 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天"}', 'name': 'Query_Timely_Weather'}, 'id': 'call_jwuxmpbbnq7d298973chwgqm', 'type': 'function'}]
2025-05-10 09:43:41.231 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0342528820037842
2025-05-10 09:43:41.231 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：实时查询天气-date:今天
INFO:     127.0.0.1:42398 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:42.406 INFO [pid-5742] @chatnlu_infer.py:99 top5：319,3,360,66,4, cost: 0.028362274169921875
2025-05-10 09:43:43.281 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Cooling_Instant'}, 'id': 'call_t2qyjyg8x6v0riydz032qt3c', 'type': 'function'}]
2025-05-10 09:43:43.282 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8761093616485596
2025-05-10 09:43:43.282 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：一键降温-无
INFO:     127.0.0.1:42402 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:43.573 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,135,428,1,410, cost: 0.02764749526977539
INFO:     127.0.0.1:42416 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:49.372 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,135,432,1,435, cost: 0.027243614196777344
2025-05-10 09:43:52.854 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:43:52.857 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:43:52.857 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.4850494861602783
INFO:     127.0.0.1:57358 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:43:52.917 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,135,2, cost: 0.026232242584228516
INFO:     127.0.0.1:57362 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:02.173 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,135,354,428, cost: 0.02626800537109375
INFO:     127.0.0.1:46770 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:04.447 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,354,2, cost: 0.026067256927490234
INFO:     127.0.0.1:46778 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:08.387 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,102, cost: 0.025720834732055664
2025-05-10 09:44:09.378 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_hplz0eq71g1mfewx6vu3kn08', 'type': 'function'}]
2025-05-10 09:44:09.379 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9919846057891846
2025-05-10 09:44:09.379 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:40536 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:09.808 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,292,428,2, cost: 0.02624654769897461
INFO:     127.0.0.1:40544 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:13.761 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,432,1,428,22, cost: 0.0262453556060791
INFO:     127.0.0.1:40548 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:14.854 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,24,45,428, cost: 0.02632737159729004
INFO:     127.0.0.1:40556 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:18.949 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,235,22,150, cost: 0.02507472038269043
2025-05-10 09:44:19.780 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:44:19.782 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:44:19.783 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.833613395690918
INFO:     127.0.0.1:47924 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:22.650 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,304,50,96,428, cost: 0.02597784996032715
2025-05-10 09:44:27.070 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:44:27.072 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:44:27.073 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.422550678253174
INFO:     127.0.0.1:47934 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:27.543 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,304,50, cost: 0.024443864822387695
INFO:     127.0.0.1:47936 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:30.614 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,432,354,292,1, cost: 0.02604842185974121
2025-05-10 09:44:33.181 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:44:33.183 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:44:33.183 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.5691657066345215
INFO:     127.0.0.1:55302 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:35.875 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,432,42, cost: 0.0280148983001709
INFO:     127.0.0.1:55304 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:39.650 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,428,8, cost: 0.03374338150024414
INFO:     127.0.0.1:48246 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:44.236 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,432,1,428,22, cost: 0.03165292739868164
2025-05-10 09:44:47.604 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:44:47.606 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:44:47.607 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.3711295127868652
INFO:     127.0.0.1:48252 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:47.656 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,3,137,22, cost: 0.020165205001831055
2025-05-10 09:44:48.859 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "春熙路"}', 'name': 'Go_POI'}, 'id': 'call_3ey28ywv80h3yqe6t68tc7o1', 'type': 'function'}]
2025-05-10 09:44:48.860 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2037687301635742
2025-05-10 09:44:48.860 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:春熙路
INFO:     127.0.0.1:48262 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:50.249 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,150,22, cost: 0.02599501609802246
2025-05-10 09:44:51.380 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "好玩的", "Target": "春熙路"}', 'name': 'Go_POI'}, 'id': 'call_9hyhadavdj9fgo87vzbs4e31', 'type': 'function'}]
2025-05-10 09:44:51.381 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1313388347625732
2025-05-10 09:44:51.382 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:好玩的,landmark:春熙路
INFO:     127.0.0.1:42548 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:51.436 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,102,8, cost: 0.024658918380737305
2025-05-10 09:44:52.314 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "寺庙"}', 'name': 'Go_POI'}, 'id': 'call_u27ilfedlg6f3isti4tzhslf', 'type': 'function'}]
2025-05-10 09:44:52.315 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8785450458526611
2025-05-10 09:44:52.315 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:寺庙
INFO:     127.0.0.1:42564 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:53.441 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,192,156,213, cost: 0.03205990791320801
2025-05-10 09:44:54.290 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天"}', 'name': 'Ask_Sport'}, 'id': 'call_z991lhilsrylcjaorieems09', 'type': 'function'}]
2025-05-10 09:44:54.291 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8499481678009033
2025-05-10 09:44:54.292 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查询运动-date:今天
INFO:     127.0.0.1:42576 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:54.586 INFO [pid-5742] @chatnlu_infer.py:99 top5：311,46,305,190,32, cost: 0.032212018966674805
2025-05-10 09:44:55.323 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Nav_Zoom_Out'}, 'id': 'call_vnb6ese1316uxflbql682dt1', 'type': 'function'}]
2025-05-10 09:44:55.324 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7374024391174316
2025-05-10 09:44:55.324 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：缩小地图-无
INFO:     127.0.0.1:42588 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:56.865 INFO [pid-5742] @chatnlu_infer.py:99 top5：42,117,294,365,23, cost: 0.0252225399017334
2025-05-10 09:44:57.556 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Close_Nav'}, 'id': 'call_37qtts7sb2xm1u7jb8110fat', 'type': 'function'}]
2025-05-10 09:44:57.557 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.6914961338043213
2025-05-10 09:44:57.557 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：关闭导航-无
INFO:     127.0.0.1:42594 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:44:57.926 INFO [pid-5742] @chatnlu_infer.py:99 top5：88,356,327,162,14, cost: 0.02566051483154297
2025-05-10 09:44:58.812 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Air_Condition'}, 'id': 'call_jxwxoqwj33dkd2nr37nwrwjv', 'type': 'function'}]
2025-05-10 09:44:58.813 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8870010375976562
2025-05-10 09:44:58.813 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开空调-无
INFO:     127.0.0.1:48484 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:02.200 INFO [pid-5742] @chatnlu_infer.py:99 top5：162,3,360,319,4, cost: 0.02842402458190918
2025-05-10 09:45:07.346 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:45:07.346 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:45:07.346 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.146212816238403
INFO:     127.0.0.1:48492 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:07.381 INFO [pid-5742] @chatnlu_infer.py:99 top5：183,88,266,160,189, cost: 0.024709224700927734
2025-05-10 09:45:08.718 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Position": ""}', 'name': 'Open_Air_Condition'}, 'id': 'call_6wphg4weztjtqbqhffkhjyoo', 'type': 'function'}, {'function': {'arguments': '{"Direction": "吹脸"}', 'name': 'Set_Wind_Direction'}, 'id': 'call_r0164a45bzp0qrb4lbz2xum4', 'type': 'function'}]
2025-05-10 09:45:08.719 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.337789535522461
2025-05-10 09:45:08.719 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开空调-无
INFO:     127.0.0.1:48504 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:09.543 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,45,88,260,17, cost: 0.027195453643798828
INFO:     127.0.0.1:36356 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:13.167 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,59,288, cost: 0.028430700302124023
2025-05-10 09:45:14.337 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "加油站", "Target": "当前位置", "index": "1"}', 'name': 'Go_POI'}, 'id': 'call_h95krae5tnbzot64i3wb0o63', 'type': 'function'}]
2025-05-10 09:45:14.338 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1703202724456787
2025-05-10 09:45:14.338 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:加油站,landmark:当前位置,选项:1
INFO:     127.0.0.1:36360 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:15.663 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,432,428,102, cost: 0.026769638061523438
2025-05-10 09:45:16.716 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "加油站"}', 'name': 'Go_POI'}, 'id': 'call_q64v0nas3swa9hkcaziupmmc', 'type': 'function'}]
2025-05-10 09:45:16.717 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0533714294433594
2025-05-10 09:45:16.717 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:加油站
INFO:     127.0.0.1:36366 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:18.961 INFO [pid-5742] @chatnlu_infer.py:99 top5：15,167,136,4,162, cost: 0.027009010314941406
2025-05-10 09:45:20.259 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Number": "24", "Position": "", "Ratio": ""}', 'name': 'Set_Air_Condition_Temperature'}, 'id': 'call_ayf709yfnh49t8bqxvmcvppy', 'type': 'function'}]
2025-05-10 09:45:20.260 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2979936599731445
2025-05-10 09:45:20.260 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置空调温度-number:24
INFO:     127.0.0.1:40540 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:21.128 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,45,260,17,319, cost: 0.025310993194580078
2025-05-10 09:45:22.201 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "level": "凉快"}', 'name': 'Ask_Wind'}, 'id': 'call_662hla60lahw1ywys8me8gnh', 'type': 'function'}]
2025-05-10 09:45:22.202 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0740950107574463
2025-05-10 09:45:22.203 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查询风力-date:今天,风力:凉快
INFO:     127.0.0.1:40556 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:22.530 INFO [pid-5742] @chatnlu_infer.py:99 top5：402,194,120,292,180, cost: 0.025649309158325195
2025-05-10 09:45:24.025 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "明天", "fog": "", "location": "", "quilt": "", "rain": "", "snow": "", "sun": ""}', 'name': 'Query_Weather'}, 'id': 'call_shpw4tudq90vh5pynirdhlxs', 'type': 'function'}]
2025-05-10 09:45:24.027 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.49709153175354
2025-05-10 09:45:24.027 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:明天
INFO:     127.0.0.1:40562 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:24.820 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,2,1,40, cost: 0.025548219680786133
INFO:     127.0.0.1:40572 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:25.862 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,59,3, cost: 0.023421525955200195
2025-05-10 09:45:27.031 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "餐厅", "Target": "当前位置"}', 'name': 'Go_POI'}, 'id': 'call_shssu4cjw1lzfdq7xzszd5hg', 'type': 'function'}]
2025-05-10 09:45:27.031 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1696548461914062
2025-05-10 09:45:27.032 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:餐厅,landmark:当前位置
INFO:     127.0.0.1:40586 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:27.886 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,125,1,196,292, cost: 0.02539849281311035
2025-05-10 09:45:29.057 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_mjwxbf19eguln5xpdmuosseh', 'type': 'function'}]
2025-05-10 09:45:29.059 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1725437641143799
2025-05-10 09:45:29.059 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:42640 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:31.497 INFO [pid-5742] @chatnlu_infer.py:99 top5：210,137,32,339,54, cost: 0.026296377182006836
2025-05-10 09:45:32.465 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"家POI": "目的地"}', 'name': 'Nav_Set_Home'}, 'id': 'call_gz3c41jqa7u3azgl5mg9haye', 'type': 'function'}]
2025-05-10 09:45:32.466 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9680290222167969
2025-05-10 09:45:32.466 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置家地址-POI:目的地
INFO:     127.0.0.1:42654 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:33.281 INFO [pid-5742] @chatnlu_infer.py:99 top5：54,152,27,340,179, cost: 0.0261533260345459
2025-05-10 09:45:34.032 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Home_Condition'}, 'id': 'call_xf721hvj8beb6iqim7puo1ne', 'type': 'function'}]
2025-05-10 09:45:34.033 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7514529228210449
2025-05-10 09:45:34.033 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：家路况-无
INFO:     127.0.0.1:42664 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:34.425 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,431,312,103, cost: 0.02532362937927246
2025-05-10 09:45:35.501 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "周杰伦", "Name": "青花瓷"}', 'name': 'Search_Music'}, 'id': 'call_6jox3vagx7xx30ml68zuysi8', 'type': 'function'}]
2025-05-10 09:45:35.501 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0762152671813965
2025-05-10 09:45:35.502 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:周杰伦,歌曲:青花瓷
INFO:     127.0.0.1:42672 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:36.540 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,178,429,250, cost: 0.02643108367919922
INFO:     127.0.0.1:42676 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:38.950 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,1,44, cost: 0.017211437225341797
2025-05-10 09:45:40.213 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "周杰伦", "album": ""}', 'name': 'Search_Music'}, 'id': 'call_si1rgoi7yhxo70blkfkx7849', 'type': 'function'}]
2025-05-10 09:45:40.214 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2633795738220215
2025-05-10 09:45:40.214 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:周杰伦
INFO:     127.0.0.1:44212 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:44.269 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,215,347,277, cost: 0.025849342346191406
2025-05-10 09:45:48.563 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:45:48.565 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:45:48.566 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.296097993850708
INFO:     127.0.0.1:44218 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:48.622 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,317,178,221, cost: 0.025785446166992188
2025-05-10 09:45:49.834 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "周杰伦", "charts": "热门歌曲"}', 'name': 'Search_Music'}, 'id': 'call_k35ipsrjtgvepus250g78is1', 'type': 'function'}]
2025-05-10 09:45:49.835 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2131292819976807
2025-05-10 09:45:49.835 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:周杰伦,榜单:热门歌曲
INFO:     127.0.0.1:51148 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:54.696 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,103,363,51, cost: 0.026093482971191406
2025-05-10 09:45:55.267 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:45:55.269 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:45:55.270 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.572899341583252
INFO:     127.0.0.1:51150 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:55.674 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,428,1, cost: 0.025646448135375977
INFO:     127.0.0.1:51156 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:45:59.332 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,8,178,103, cost: 0.02624821662902832
2025-05-10 09:46:00.965 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:46:00.967 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:46:00.968 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.635263204574585
INFO:     127.0.0.1:50028 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:03.395 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,51,431,1, cost: 0.02453303337097168
2025-05-10 09:46:04.767 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "七里香", "Singer": "周杰伦"}', 'name': 'Search_Music'}, 'id': 'call_r97nqvchbx81rie5vrx8la6i', 'type': 'function'}]
2025-05-10 09:46:04.768 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.3731091022491455
2025-05-10 09:46:04.768 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:七里香,歌手:周杰伦
INFO:     127.0.0.1:50032 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:10.966 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,51,433,431, cost: 0.025241851806640625
2025-05-10 09:46:13.075 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:46:13.077 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:46:13.077 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.1111199855804443
INFO:     127.0.0.1:59542 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:15.687 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,431,72, cost: 0.026077985763549805
2025-05-10 09:46:17.552 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:46:17.554 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:46:17.555 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.8674814701080322
INFO:     127.0.0.1:59544 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:20.473 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,431,178,51, cost: 0.025759458541870117
INFO:     127.0.0.1:32956 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:27.912 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,430, cost: 0.026257753372192383
INFO:     127.0.0.1:43974 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:35.669 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,293, cost: 0.025029420852661133
INFO:     127.0.0.1:43986 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:40.191 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,293,1, cost: 0.02629542350769043
INFO:     127.0.0.1:47506 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:44.475 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,293, cost: 0.02595829963684082
INFO:     127.0.0.1:47512 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:48.549 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,214,313,172,171, cost: 0.027658700942993164
2025-05-10 09:46:49.038 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:46:49.040 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:46:49.041 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.49091076850891113
INFO:     127.0.0.1:39060 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:52.627 INFO [pid-5742] @chatnlu_infer.py:99 top5：214,324,31,172,313, cost: 0.025480031967163086
2025-05-10 09:46:53.583 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Emergency": "120"}', 'name': 'Call_Emergency'}, 'id': 'call_6liymsenygyhzfgxpybvfixl', 'type': 'function'}]
2025-05-10 09:46:53.584 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9567117691040039
2025-05-10 09:46:53.584 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打紧急电话-紧急电话:120
INFO:     127.0.0.1:39068 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:53.880 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,346,1, cost: 0.025571107864379883
INFO:     127.0.0.1:39084 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:46:58.482 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,430,293, cost: 0.02670001983642578
INFO:     127.0.0.1:47670 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:02.289 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,293,194, cost: 0.026274442672729492
INFO:     127.0.0.1:47674 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:08.301 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,150, cost: 0.026598215103149414
INFO:     127.0.0.1:59568 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:16.512 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,293, cost: 0.026615142822265625
INFO:     127.0.0.1:59574 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:19.684 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,319,354, cost: 0.02613544464111328
INFO:     127.0.0.1:56604 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:25.254 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,293, cost: 0.026489973068237305
INFO:     127.0.0.1:56606 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:31.559 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,346,2, cost: 0.02559661865234375
INFO:     127.0.0.1:40318 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:37.091 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,293, cost: 0.02571868896484375
INFO:     127.0.0.1:40328 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:40.877 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,150,2,1,428, cost: 0.024718046188354492
INFO:     127.0.0.1:49158 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:47.210 INFO [pid-5742] @chatnlu_infer.py:99 top5：125,3,196,2,130, cost: 0.026571989059448242
2025-05-10 09:47:52.354 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:47:52.354 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:47:52.354 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.143648147583008
INFO:     127.0.0.1:49174 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:52.385 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,59, cost: 0.02982044219970703
2025-05-10 09:47:53.428 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "春熙路"}', 'name': 'Go_POI'}, 'id': 'call_k9vbz5jz6zifjqig8kiezi5x', 'type': 'function'}]
2025-05-10 09:47:53.429 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0430107116699219
2025-05-10 09:47:53.429 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:春熙路
INFO:     127.0.0.1:42338 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:54.232 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,8, cost: 0.025112628936767578
2025-05-10 09:47:55.365 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "餐厅", "Target": "春熙路"}', 'name': 'Go_POI'}, 'id': 'call_a0ik0hcbzod6yubgqa1r0s51', 'type': 'function'}]
2025-05-10 09:47:55.366 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1334991455078125
2025-05-10 09:47:55.366 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:餐厅,landmark:春熙路
INFO:     127.0.0.1:42340 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:56.444 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,8, cost: 0.01611328125
2025-05-10 09:47:57.960 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "评分过4.5的餐厅", "Target": "春熙路"}', 'name': 'Go_POI'}, 'id': 'call_yf1g6ka3e6scxld4s0l95sdz', 'type': 'function'}]
2025-05-10 09:47:57.961 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.516814947128296
2025-05-10 09:47:57.961 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:评分过4.5的餐厅,landmark:春熙路
INFO:     127.0.0.1:42346 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:47:58.007 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,150, cost: 0.015721797943115234
INFO:     127.0.0.1:45144 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:02.948 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,125,150,324, cost: 0.012739181518554688
2025-05-10 09:48:03.934 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Yellow": "牙医预约电话"}', 'name': 'Call_Yellow_Page'}, 'id': 'call_hptsxx371invoo4r8ajsck81', 'type': 'function'}]
2025-05-10 09:48:03.935 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9865002632141113
2025-05-10 09:48:03.935 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打黄页-黄页电话:牙医预约电话
INFO:     127.0.0.1:45160 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:07.945 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,150, cost: 0.026320934295654297
INFO:     127.0.0.1:41142 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:12.129 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,178,428, cost: 0.02567267417907715
INFO:     127.0.0.1:41156 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:16.766 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,8,150, cost: 0.025933265686035156
INFO:     127.0.0.1:41162 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:22.144 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,293, cost: 0.025781869888305664
INFO:     127.0.0.1:50004 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:26.066 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,293, cost: 0.025361061096191406
INFO:     127.0.0.1:50008 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:30.244 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,3,312,431, cost: 0.026127338409423828
2025-05-10 09:48:31.140 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "野人"}', 'name': 'Search_Music'}, 'id': 'call_cfmetwu1jujc37t0c49vkvai', 'type': 'function'}]
2025-05-10 09:48:31.142 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8971936702728271
2025-05-10 09:48:31.142 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:野人
INFO:     127.0.0.1:42510 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:32.398 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,8, cost: 0.026334762573242188
INFO:     127.0.0.1:42514 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:34.882 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,51,312,215, cost: 0.02604818344116211
2025-05-10 09:48:35.988 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "冷狗儿", "album": "野人"}', 'name': 'Search_Music'}, 'id': 'call_n7qv6yavhhmk2hcp773tn704', 'type': 'function'}]
2025-05-10 09:48:35.989 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1072871685028076
2025-05-10 09:48:35.989 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:冷狗儿,专辑:野人
INFO:     127.0.0.1:42518 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:37.060 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,431,103,363, cost: 0.026566505432128906
2025-05-10 09:48:38.282 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "冷狗儿", "Name": "野人"}', 'name': 'Search_Music'}, 'id': 'call_sdkxk5spa8xav1keuo8pg1pv', 'type': 'function'}]
2025-05-10 09:48:38.283 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2219483852386475
2025-05-10 09:48:38.283 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:冷狗儿,歌曲:野人
INFO:     127.0.0.1:42530 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:38.341 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,3,137,59, cost: 0.024956464767456055
2025-05-10 09:48:39.389 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "川菜馆", "Target": "当前位置"}', 'name': 'Go_POI'}, 'id': 'call_1eu8t06yv83bt1p2ep417dnw', 'type': 'function'}]
2025-05-10 09:48:39.390 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0481462478637695
2025-05-10 09:48:39.390 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:川菜馆,landmark:当前位置
INFO:     127.0.0.1:42576 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:40.368 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,2,150,428, cost: 0.025948286056518555
2025-05-10 09:48:45.510 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:48:45.510 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:48:45.510 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.141671895980835
INFO:     127.0.0.1:42592 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:48.751 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,293,1,428,430, cost: 0.025162220001220703
INFO:     127.0.0.1:56646 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:51.923 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,259,292,22, cost: 0.026065587997436523
2025-05-10 09:48:57.069 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:48:57.069 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:48:57.069 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.146148681640625
INFO:     127.0.0.1:56650 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:48:57.100 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,125,2,428, cost: 0.023878097534179688
INFO:     127.0.0.1:56652 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:00.480 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,22,428,130, cost: 0.013897418975830078
2025-05-10 09:49:04.051 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:49:04.053 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:49:04.054 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.5736021995544434
INFO:     127.0.0.1:51396 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:06.933 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,293,1,2, cost: 0.025009870529174805
INFO:     127.0.0.1:51410 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:11.287 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,428,346, cost: 0.026181459426879883
2025-05-10 09:49:12.229 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "公安局"}', 'name': 'Go_POI'}, 'id': 'call_vj8hsbw2iz9sxbv3tk6supeh', 'type': 'function'}]
2025-05-10 09:49:12.230 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9427645206451416
2025-05-10 09:49:12.230 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:公安局
INFO:     127.0.0.1:38794 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:14.916 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,137,22, cost: 0.02709674835205078
2025-05-10 09:49:16.527 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:49:16.531 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:49:16.531 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.615081787109375
INFO:     127.0.0.1:38810 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:17.877 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,8,150, cost: 0.026820659637451172
INFO:     127.0.0.1:36184 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:23.091 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,431,312,103, cost: 0.026262521743774414
2025-05-10 09:49:24.178 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "赵雷", "Name": "成都"}', 'name': 'Search_Music'}, 'id': 'call_67uq9jorftyeogdiozmtmwfs', 'type': 'function'}]
2025-05-10 09:49:24.178 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.08650803565979
2025-05-10 09:49:24.178 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:赵雷,歌曲:成都
INFO:     127.0.0.1:36194 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:24.939 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,51,103,312, cost: 0.024907588958740234
2025-05-10 09:49:25.772 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:49:25.775 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:49:25.775 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8358850479125977
INFO:     127.0.0.1:36198 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:26.509 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,431,1, cost: 0.026011943817138672
2025-05-10 09:49:27.630 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "赵雷", "exclude": ""}', 'name': 'Search_Music'}, 'id': 'call_nsdld7ipa0luu1czcv98f4ol', 'type': 'function'}]
2025-05-10 09:49:27.630 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.12060546875
2025-05-10 09:49:27.630 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:赵雷
INFO:     127.0.0.1:36214 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:27.686 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,312,363,103,431, cost: 0.025680065155029297
2025-05-10 09:49:28.676 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "天堂"}', 'name': 'Search_Music'}, 'id': 'call_dhylktpnpcn6vomwteogln3p', 'type': 'function'}]
2025-05-10 09:49:28.676 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9893884658813477
2025-05-10 09:49:28.676 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:天堂
INFO:     127.0.0.1:36222 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:29.671 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,51,312,363, cost: 0.024383544921875
2025-05-10 09:49:30.764 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "华晨宇", "Name": "天堂"}', 'name': 'Search_Music'}, 'id': 'call_de82tp975k6tzyb15klba0zw', 'type': 'function'}]
2025-05-10 09:49:30.765 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0936088562011719
2025-05-10 09:49:30.765 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:华晨宇,歌曲:天堂
INFO:     127.0.0.1:43802 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:30.863 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,125,150,51, cost: 0.025476455688476562
INFO:     127.0.0.1:43806 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:37.353 INFO [pid-5742] @chatnlu_infer.py:99 top5：125,196,130,1,2, cost: 0.02482295036315918
2025-05-10 09:49:38.467 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"app": "少年西游记2"}', 'name': 'Open_App'}, 'id': 'call_sxhqlm3wyuvk1lyi4slsfrfb', 'type': 'function'}]
2025-05-10 09:49:38.467 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1139161586761475
2025-05-10 09:49:38.467 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开应用-非多媒体应用:少年西游记2
INFO:     127.0.0.1:43822 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:38.518 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,51,150,1, cost: 0.02258586883544922
2025-05-10 09:49:43.618 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:49:43.618 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:49:43.618 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.100117206573486
INFO:     127.0.0.1:35934 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:45.037 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,431,103,312, cost: 0.016588926315307617
2025-05-10 09:49:46.156 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "李白", "Keywords": "诗"}', 'name': 'Search_Music'}, 'id': 'call_8ibuc744xw1i3fxtq4pgyzpz', 'type': 'function'}]
2025-05-10 09:49:46.157 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1194634437561035
2025-05-10 09:49:46.157 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:李白,歌曲关键字:诗
INFO:     127.0.0.1:35950 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:46.256 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,431,178,317, cost: 0.02556467056274414
2025-05-10 09:49:47.139 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"TV": "喜欢你"}', 'name': 'Search_Music'}, 'id': 'call_7m2cp978y8pi1tqa05yhjb62', 'type': 'function'}]
2025-05-10 09:49:47.140 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8830204010009766
2025-05-10 09:49:47.140 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-电视剧:喜欢你
INFO:     127.0.0.1:35966 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:53.891 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,363,312, cost: 0.026635169982910156
2025-05-10 09:49:55.402 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "陈绮贞", "Name": "我喜欢上你时的内心活动"}', 'name': 'Search_Music'}, 'id': 'call_8rfkejgwojdnel0tvovokb9r', 'type': 'function'}]
2025-05-10 09:49:55.402 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.5113933086395264
2025-05-10 09:49:55.403 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:陈绮贞,歌曲:我喜欢上你时的内心活动
INFO:     127.0.0.1:42414 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:55.496 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,431,103,312, cost: 0.025686264038085938
2025-05-10 09:49:56.262 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_np5i1wz1sthywbk430hl4cvr', 'type': 'function'}]
2025-05-10 09:49:56.263 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7667953968048096
2025-05-10 09:49:56.263 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:42426 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:49:57.454 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,51,431,150, cost: 0.026848793029785156
2025-05-10 09:49:58.755 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "我喜欢上你时的内心活动"}', 'name': 'Search_Music'}, 'id': 'call_nqjmduhh5sm3ux3xbef8ia06', 'type': 'function'}]
2025-05-10 09:49:58.756 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.302344799041748
2025-05-10 09:49:58.756 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:我喜欢上你时的内心活动
INFO:     127.0.0.1:42436 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:02.918 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,317,277,178, cost: 0.025545120239257812
2025-05-10 09:50:04.394 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"album": "我喜欢上你时的内心活动", "Name": "我喜欢上你时的内心活动"}', 'name': 'Search_Music'}, 'id': 'call_lob8svrsylvvfu1vu9y21rnz', 'type': 'function'}]
2025-05-10 09:50:04.395 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.4767374992370605
2025-05-10 09:50:04.395 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-专辑:我喜欢上你时的内心活动,歌曲:我喜欢上你时的内心活动
INFO:     127.0.0.1:46748 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:04.497 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,178,317,312, cost: 0.026163339614868164
2025-05-10 09:50:05.499 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "还是会寂寞"}', 'name': 'Search_Music'}, 'id': 'call_rl3vekmn25lwxteuj1grzkw9', 'type': 'function'}]
2025-05-10 09:50:05.500 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0032141208648682
2025-05-10 09:50:05.500 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:还是会寂寞
INFO:     127.0.0.1:46752 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:11.613 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,51,312,363, cost: 0.026524066925048828
2025-05-10 09:50:12.820 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"album": "还是会寂寞", "Singer": "陈绮贞"}', 'name': 'Search_Music'}, 'id': 'call_3nlg9ngnl48niwnd4ga5c4qx', 'type': 'function'}]
2025-05-10 09:50:12.821 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2084531784057617
2025-05-10 09:50:12.822 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-专辑:还是会寂寞,歌手:陈绮贞
INFO:     127.0.0.1:55778 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:14.605 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,3,312,51, cost: 0.03135275840759277
2025-05-10 09:50:15.766 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"album": "还是会寂寞", "Singer": "陈绮贞"}', 'name': 'Search_Music'}, 'id': 'call_muy6dqop5zt0scngvs80xjyq', 'type': 'function'}]
2025-05-10 09:50:15.767 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1616473197937012
2025-05-10 09:50:15.767 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-专辑:还是会寂寞,歌手:陈绮贞
INFO:     127.0.0.1:55790 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:15.861 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,63,1,8, cost: 0.02736496925354004
INFO:     127.0.0.1:55802 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:21.800 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,63,1,293, cost: 0.025230884552001953
INFO:     127.0.0.1:58252 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:25.520 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,293,221, cost: 0.025948524475097656
INFO:     127.0.0.1:58268 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:30.203 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,317,178,221, cost: 0.02634429931640625
2025-05-10 09:50:31.343 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "少女标本"}', 'name': 'Search_Music'}, 'id': 'call_2t8vgwh997k81ww95yvo3kf1', 'type': 'function'}]
2025-05-10 09:50:31.344 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1402649879455566
2025-05-10 09:50:31.344 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:少女标本
INFO:     127.0.0.1:35742 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:34.734 INFO [pid-5742] @chatnlu_infer.py:99 top5：156,3,213,337,200, cost: 0.025660037994384766
2025-05-10 09:50:35.728 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "location": "成都市"}', 'name': 'Ask_Fishing'}, 'id': 'call_xdd0sk6x8bow6lcb5nd401h6', 'type': 'function'}]
2025-05-10 09:50:35.729 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9944314956665039
2025-05-10 09:50:35.729 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查询钓鱼-date:今天,city:成都市
INFO:     127.0.0.1:35746 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:36.853 INFO [pid-5742] @chatnlu_infer.py:99 top5：156,3,213,337,1, cost: 0.02629256248474121
2025-05-10 09:50:37.962 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "location": "锦江区"}', 'name': 'Ask_Fishing'}, 'id': 'call_q0421gshuggulqeqjj1eo8gl', 'type': 'function'}]
2025-05-10 09:50:37.963 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.110119342803955
2025-05-10 09:50:37.963 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查询钓鱼-date:今天,city:锦江区
INFO:     127.0.0.1:35762 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:39.606 INFO [pid-5742] @chatnlu_infer.py:99 top5：156,3,213,337,200, cost: 0.026360511779785156
2025-05-10 09:50:40.613 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "location": "北京朝阳区"}', 'name': 'Ask_Fishing'}, 'id': 'call_bxulhlvq4ht809m86b8er97y', 'type': 'function'}]
2025-05-10 09:50:40.614 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0067811012268066
2025-05-10 09:50:40.614 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查询钓鱼-date:今天,city:北京朝阳区
INFO:     127.0.0.1:50402 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:41.806 INFO [pid-5742] @chatnlu_infer.py:99 top5：156,3,213,337,200, cost: 0.027712583541870117
2025-05-10 09:50:42.940 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "location": "长春朝阳区"}', 'name': 'Ask_Fishing'}, 'id': 'call_um096kt309k3pvtzqywd3yol', 'type': 'function'}]
2025-05-10 09:50:42.940 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.134000539779663
2025-05-10 09:50:42.941 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查询钓鱼-date:今天,city:长春朝阳区
INFO:     127.0.0.1:50404 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:43.001 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,200,402,376,180, cost: 0.026207685470581055
2025-05-10 09:50:44.644 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "fog": "", "location": "朝阳区", "quilt": "", "rain": "会", "snow": "", "sun": ""}', 'name': 'Query_Weather'}, 'id': 'call_iuo8u9jw01xqtkbpfkl7fhtj', 'type': 'function'}]
2025-05-10 09:50:44.645 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.6438302993774414
2025-05-10 09:50:44.645 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:今天,city:朝阳区,rain:会
INFO:     127.0.0.1:50408 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:45.752 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,200,376,402,180, cost: 0.025077342987060547
2025-05-10 09:50:47.305 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "fog": "", "location": "北京市朝阳区", "quilt": "", "rain": "会", "snow": "", "sun": ""}', 'name': 'Query_Weather'}, 'id': 'call_gqkh64uggpm40i3yd8677qtf', 'type': 'function'}]
2025-05-10 09:50:47.306 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.5540988445281982
2025-05-10 09:50:47.306 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:今天,city:北京市朝阳区,rain:会
INFO:     127.0.0.1:50420 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:48.339 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,200,376,402,180, cost: 0.02564859390258789
2025-05-10 09:50:49.519 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "location": "长春市朝阳区", "rain": "会"}', 'name': 'Query_Weather'}, 'id': 'call_2dirr0h048nzz3u8j3as32zy', 'type': 'function'}]
2025-05-10 09:50:49.519 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1796636581420898
2025-05-10 09:50:49.519 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:今天,city:长春市朝阳区,rain:会
INFO:     127.0.0.1:54462 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:49.616 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,346,150,292, cost: 0.024960994720458984
INFO:     127.0.0.1:54472 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:50:53.442 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,103,51,317, cost: 0.024457454681396484
2025-05-10 09:50:54.407 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:50:54.409 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:50:54.410 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9678187370300293
INFO:     127.0.0.1:54476 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:00.321 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,51,312,363, cost: 0.0268094539642334
2025-05-10 09:51:01.423 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "周杰伦", "Name": "千里之外"}', 'name': 'Search_Music'}, 'id': 'call_wcvzekaevlppchfhmcmiug8z', 'type': 'function'}]
2025-05-10 09:51:01.423 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.101900339126587
2025-05-10 09:51:01.424 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:周杰伦,歌曲:千里之外
INFO:     127.0.0.1:45414 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:02.572 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,199,293,13,155, cost: 0.02553534507751465
2025-05-10 09:51:03.235 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_ogrkehyetbeee5vcwv7u9v9s', 'type': 'function'}]
2025-05-10 09:51:03.236 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.6635634899139404
2025-05-10 09:51:03.236 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:45422 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:07.624 INFO [pid-5742] @chatnlu_infer.py:99 top5：199,92,155,243,414, cost: 0.025525569915771484
2025-05-10 09:51:08.426 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_DashCam'}, 'id': 'call_jxtw9qov4sbwafgxe230lzq2', 'type': 'function'}]
2025-05-10 09:51:08.427 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8013401031494141
2025-05-10 09:51:08.427 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开行车记录仪-无
INFO:     127.0.0.1:45432 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:09.746 INFO [pid-5742] @chatnlu_infer.py:99 top5：199,155,108,143,6, cost: 0.025949478149414062
2025-05-10 09:51:10.674 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Media_Source": "行车记录仪"}', 'name': 'Open_Play_History'}, 'id': 'call_2ug1ubgbehfbqgplfvzu2nty', 'type': 'function'}]
2025-05-10 09:51:10.675 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.929081916809082
2025-05-10 09:51:10.675 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开播放历史-媒体源:行车记录仪
INFO:     127.0.0.1:59930 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:11.573 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,22,8, cost: 0.02562856674194336
2025-05-10 09:51:12.643 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "椿树街和月季街的交叉路口"}', 'name': 'Go_POI'}, 'id': 'call_olj9lofy5guvsk59atqspt0n', 'type': 'function'}]
2025-05-10 09:51:12.644 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0706815719604492
2025-05-10 09:51:12.644 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:椿树街和月季街的交叉路口
INFO:     127.0.0.1:59944 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:13.595 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,102,59, cost: 0.02852916717529297
2025-05-10 09:51:14.877 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "奈雪的茶"}', 'name': 'Go_POI'}, 'id': 'call_hnq7j50deriyigwuqmmia5k0', 'type': 'function'}]
2025-05-10 09:51:14.878 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2830109596252441
2025-05-10 09:51:14.878 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:奈雪的茶
INFO:     127.0.0.1:59956 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:14.936 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,150,125, cost: 0.026429414749145508
2025-05-10 09:51:16.114 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Type": "娱乐", "Key": "热门上映的电影"}', 'name': 'Search_News'}, 'id': 'call_2fh6z1dencnr6khpwtn2tmq0', 'type': 'function'}]
2025-05-10 09:51:16.115 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1793558597564697
2025-05-10 09:51:16.115 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：新闻搜索-新闻类型:娱乐,新闻关键字:热门上映的电影
INFO:     127.0.0.1:59968 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:20.762 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,51,431,363, cost: 0.014276742935180664
2025-05-10 09:51:21.896 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"TV": "幽灵公主", "Name": "幽灵公主的主题曲"}', 'name': 'Search_Music'}, 'id': 'call_7utgkyjxohp6kw5jk32ham4a', 'type': 'function'}]
2025-05-10 09:51:21.897 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1343541145324707
2025-05-10 09:51:21.897 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-电视剧:幽灵公主,歌曲:幽灵公主的主题曲
INFO:     127.0.0.1:53458 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:21.996 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,150, cost: 0.02637171745300293
2025-05-10 09:51:23.432 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Keywords": "东北烧烤", "Scene": "吃饭"}', 'name': 'Search_Music'}, 'id': 'call_vrv4qnayccp47l7tuz4zjqbv', 'type': 'function'}]
2025-05-10 09:51:23.433 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.4361052513122559
2025-05-10 09:51:23.433 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲关键字:东北烧烤,歌曲场景:吃饭
INFO:     127.0.0.1:53464 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:30.042 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,59,22, cost: 0.017334938049316406
2025-05-10 09:51:31.065 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "东北烧烤店"}', 'name': 'Go_POI'}, 'id': 'call_75q1fce7homtro7fylaujkva', 'type': 'function'}]
2025-05-10 09:51:31.066 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0238196849822998
2025-05-10 09:51:31.066 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:东北烧烤店
INFO:     127.0.0.1:41340 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:31.224 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,137,2, cost: 0.025476694107055664
2025-05-10 09:51:32.430 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "成都市", "POI": "大熊猫繁育研究基地"}', 'name': 'Go_POI'}, 'id': 'call_sbqnihtfji59acu53bh46rki', 'type': 'function'}]
2025-05-10 09:51:32.431 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2067391872406006
2025-05-10 09:51:32.432 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:成都市,POI:大熊猫繁育研究基地
INFO:     127.0.0.1:41350 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:33.509 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,22,292,102, cost: 0.03088235855102539
2025-05-10 09:51:35.856 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:51:35.858 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:51:35.859 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.3492422103881836
INFO:     127.0.0.1:41356 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:35.886 INFO [pid-5742] @chatnlu_infer.py:99 top5：432,3,292,1,428, cost: 0.02559208869934082
2025-05-10 09:51:37.043 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"gas": "1"}', 'name': 'Check_Car_Condition'}, 'id': 'call_ld9jh86ythfepvwz0yb09784', 'type': 'function'}]
2025-05-10 09:51:37.044 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1581766605377197
2025-05-10 09:51:37.045 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：车况检查-gas:1
INFO:     127.0.0.1:41360 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:40.104 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,432,1,22,428, cost: 0.02381753921508789
2025-05-10 09:51:41.288 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:51:41.290 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:51:41.291 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.186793565750122
INFO:     127.0.0.1:52530 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:42.547 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,319,2, cost: 0.026758670806884766
INFO:     127.0.0.1:52540 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:49.020 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,1,428, cost: 0.024968862533569336
INFO:     127.0.0.1:42690 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:51.305 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,2,137, cost: 0.025797367095947266
2025-05-10 09:51:52.412 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "芙蓉花观赏地"}', 'name': 'Go_POI'}, 'id': 'call_dmwgo8h5ni13iui33hgth6ta', 'type': 'function'}]
2025-05-10 09:51:52.413 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.107313632965088
2025-05-10 09:51:52.413 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:芙蓉花观赏地
INFO:     127.0.0.1:42702 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:56.013 INFO [pid-5742] @chatnlu_infer.py:99 top5：324,214,411,172,31, cost: 0.02660989761352539
2025-05-10 09:51:56.981 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Yellow": "12315"}', 'name': 'Call_Yellow_Page'}, 'id': 'call_lh03j1wmrdayo7gebqid8rii', 'type': 'function'}]
2025-05-10 09:51:56.982 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9685873985290527
2025-05-10 09:51:56.982 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打黄页-黄页电话:12315
INFO:     127.0.0.1:42710 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:57.728 INFO [pid-5742] @chatnlu_infer.py:99 top5：324,214,411,172,31, cost: 0.026448965072631836
2025-05-10 09:51:58.599 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Yellow": "市长热线"}', 'name': 'Call_Yellow_Page'}, 'id': 'call_gps963top9g6ty19tdrtg3zq', 'type': 'function'}]
2025-05-10 09:51:58.600 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8715519905090332
2025-05-10 09:51:58.600 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打黄页-黄页电话:市长热线
INFO:     127.0.0.1:42718 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:51:58.845 INFO [pid-5742] @chatnlu_infer.py:99 top5：216,129,3,431,2, cost: 0.03134775161743164
2025-05-10 09:52:00.101 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Wake": "齐天大圣"}', 'name': 'Set_Wakeup_Words'}, 'id': 'call_5sxpreaiza5agy7k813oxzc7', 'type': 'function'}]
2025-05-10 09:52:00.102 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2566633224487305
2025-05-10 09:52:00.102 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：修改为指定唤醒词-唤醒词:齐天大圣
INFO:     127.0.0.1:57064 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:01.090 INFO [pid-5742] @chatnlu_infer.py:99 top5：129,216,2,3,431, cost: 0.026232481002807617
2025-05-10 09:52:02.174 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Wake": "孙悟空"}', 'name': 'Set_Wakeup_Words'}, 'id': 'call_eqy51poa2d5nf8n6cbfnpjqm', 'type': 'function'}]
2025-05-10 09:52:02.175 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0848712921142578
2025-05-10 09:52:02.175 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：修改为指定唤醒词-唤醒词:孙悟空
INFO:     127.0.0.1:57074 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:02.222 INFO [pid-5742] @chatnlu_infer.py:99 top5：61,35,62,37,304, cost: 0.021703720092773438
2025-05-10 09:52:03.500 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Position": "主驾", "Ratio": "0.5"}', 'name': 'Set_Window'}, 'id': 'call_5cffhdj4xb78k47e93l9xdgb', 'type': 'function'}]
2025-05-10 09:52:03.501 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2790286540985107
2025-05-10 09:52:03.501 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置车窗-位置:主驾,ratio:0.5
INFO:     127.0.0.1:57090 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:05.332 INFO [pid-5742] @chatnlu_infer.py:99 top5：62,35,3,195,96, cost: 0.02548813819885254
2025-05-10 09:52:09.633 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:52:09.635 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:52:09.636 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.304422616958618
INFO:     127.0.0.1:57098 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:09.664 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,165,346,2,191, cost: 0.02577352523803711
2025-05-10 09:52:14.806 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:52:14.806 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:52:14.806 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.141927719116211
INFO:     127.0.0.1:42228 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:15.729 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,63,191,2,235, cost: 0.02547430992126465
2025-05-10 09:52:16.739 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_z8im3izde1n6t7h3jso26glq', 'type': 'function'}]
2025-05-10 09:52:16.740 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0108897686004639
2025-05-10 09:52:16.740 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:42232 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:24.562 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,346,150, cost: 0.02729177474975586
INFO:     127.0.0.1:37476 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:31.849 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,255,182,293,251, cost: 0.025415897369384766
INFO:     127.0.0.1:33918 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:35.836 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,293,2,428, cost: 0.026580333709716797
INFO:     127.0.0.1:33924 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:48.622 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,135, cost: 0.03579092025756836
INFO:     127.0.0.1:36744 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:49.563 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,354,428,346, cost: 0.02569866180419922
2025-05-10 09:52:54.206 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:52:54.208 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:52:54.208 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.644606828689575
INFO:     127.0.0.1:36758 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:54.234 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,88, cost: 0.024425745010375977
INFO:     127.0.0.1:36774 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:52:59.387 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,346,319, cost: 0.025212526321411133
2025-05-10 09:53:00.278 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "吸烟区"}', 'name': 'Go_POI'}, 'id': 'call_vmnb8hnc296lj0m3wtfw4p57', 'type': 'function'}]
2025-05-10 09:53:00.279 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8908395767211914
2025-05-10 09:53:00.279 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:吸烟区
INFO:     127.0.0.1:48266 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:01.365 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,26,209,319,213, cost: 0.025957584381103516
INFO:     127.0.0.1:48278 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:04.112 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,402,120,180,209, cost: 0.026203393936157227
2025-05-10 09:53:04.994 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"location": "成都市"}', 'name': 'Query_Timely_Weather'}, 'id': 'call_3mwg2jo18sau2wxam37f503d', 'type': 'function'}]
2025-05-10 09:53:04.994 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8819906711578369
2025-05-10 09:53:04.995 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：实时查询天气-city:成都市
INFO:     127.0.0.1:48294 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:05.898 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,402,180,120,200, cost: 0.02502584457397461
2025-05-10 09:53:06.914 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "明天", "location": "上海市"}', 'name': 'Query_Weather'}, 'id': 'call_rb6rl6bcu28cxtd5s7tsd4an', 'type': 'function'}]
2025-05-10 09:53:06.915 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0167181491851807
2025-05-10 09:53:06.915 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:明天,city:上海市
INFO:     127.0.0.1:48308 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:06.981 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,171,435,133,354, cost: 0.026010990142822266
2025-05-10 09:53:07.689 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Traffic_Incidents'}, 'id': 'call_v8qpjcna8ekur6s775oqmxja', 'type': 'function'}]
2025-05-10 09:53:07.690 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.70859694480896
2025-05-10 09:53:07.690 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查看交通事件-无
INFO:     127.0.0.1:48322 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:09.521 INFO [pid-5742] @chatnlu_infer.py:99 top5：214,172,424,324,31, cost: 0.02611064910888672
2025-05-10 09:53:10.432 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Emergency": "122"}', 'name': 'Call_Emergency'}, 'id': 'call_dx4d4z7mkdn71veubiw16a1q', 'type': 'function'}]
2025-05-10 09:53:10.433 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9115872383117676
2025-05-10 09:53:10.433 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打紧急电话-紧急电话:122
INFO:     127.0.0.1:54644 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:11.266 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,8,277,208, cost: 0.0253450870513916
INFO:     127.0.0.1:54656 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:15.318 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,402,120,180,200, cost: 0.025286197662353516
2025-05-10 09:53:16.337 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"location": "成都"}', 'name': 'Query_Timely_Weather'}, 'id': 'call_iv4vg4ziz3urrnc3hzlz3n3w', 'type': 'function'}]
2025-05-10 09:53:16.338 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.02034592628479
2025-05-10 09:53:16.338 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：实时查询天气-city:成都
INFO:     127.0.0.1:54672 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:16.470 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,293, cost: 0.029505014419555664
INFO:     127.0.0.1:54682 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:21.599 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,319,293, cost: 0.027361154556274414
INFO:     127.0.0.1:34806 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:25.616 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,150,1, cost: 0.026256561279296875
2025-05-10 09:53:30.762 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:53:30.762 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:53:30.762 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.145789861679077
INFO:     127.0.0.1:34808 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:31.811 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,129,430,428, cost: 0.030676841735839844
INFO:     127.0.0.1:58576 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:36.664 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,150, cost: 0.025713443756103516
INFO:     127.0.0.1:58590 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:40.157 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,409,40,77,26, cost: 0.026306629180908203
2025-05-10 09:53:45.303 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:53:45.303 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:53:45.304 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.146369457244873
INFO:     127.0.0.1:36034 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:46.328 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,293,428, cost: 0.025697946548461914
INFO:     127.0.0.1:36036 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:50.441 INFO [pid-5742] @chatnlu_infer.py:99 top5：437,432,438,435,220, cost: 0.026201248168945312
2025-05-10 09:53:51.242 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Reserve_Maintenance'}, 'id': 'call_hch0om2wwn44tsqw20npfyrf', 'type': 'function'}]
2025-05-10 09:53:51.243 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8019907474517822
2025-05-10 09:53:51.243 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：预约维保-无
INFO:     127.0.0.1:56094 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:52.312 INFO [pid-5742] @chatnlu_infer.py:99 top5：437,438,432,435,234, cost: 0.030431032180786133
2025-05-10 09:53:53.140 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'View_Unend_Order'}, 'id': 'call_cuvy1z9ri18rfwcfd8tuyguv', 'type': 'function'}]
2025-05-10 09:53:53.140 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8282811641693115
2025-05-10 09:53:53.140 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查看未完成订单-无
INFO:     127.0.0.1:56098 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:53.274 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,56,319,209,67, cost: 0.027712106704711914
INFO:     127.0.0.1:56104 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:56.446 INFO [pid-5742] @chatnlu_infer.py:99 top5：56,121,63,123,30, cost: 0.026258230209350586
2025-05-10 09:53:57.550 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Color": "黄色或橙红色"}', 'name': 'Set_Env_Light_Color'}, 'id': 'call_ob9529depcy9s3mea951u56t', 'type': 'function'}]
2025-05-10 09:53:57.551 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1043293476104736
2025-05-10 09:53:57.551 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置氛围灯颜色-COLOR:黄色或橙红色
INFO:     127.0.0.1:56112 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:53:59.024 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,56,293,255, cost: 0.026020050048828125
INFO:     127.0.0.1:42086 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:02.424 INFO [pid-5742] @chatnlu_infer.py:99 top5：234,110,116,435,414, cost: 0.025418996810913086
2025-05-10 09:54:03.285 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'View_Unend_Order'}, 'id': 'call_7565caa01i0p63sm07cj0108', 'type': 'function'}]
2025-05-10 09:54:03.286 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.861821174621582
2025-05-10 09:54:03.286 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查看未完成订单-无
INFO:     127.0.0.1:42090 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:05.074 INFO [pid-5742] @chatnlu_infer.py:99 top5：234,110,116,414,435, cost: 0.026379108428955078
2025-05-10 09:54:05.892 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'View_Unend_Order'}, 'id': 'call_zgfdnj1g2fyvsb9py30a2hxm', 'type': 'function'}]
2025-05-10 09:54:05.892 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8182892799377441
2025-05-10 09:54:05.893 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查看未完成订单-无
INFO:     127.0.0.1:42102 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:06.362 INFO [pid-5742] @chatnlu_infer.py:99 top5：160,211,165,35,88, cost: 0.02591109275817871
2025-05-10 09:54:07.198 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Window_Diagonal'}, 'id': 'call_lv1gr3sp71wklcrwtu6ej1s9', 'type': 'function'}]
2025-05-10 09:54:07.199 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8360302448272705
2025-05-10 09:54:07.199 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开通风模式-无
INFO:     127.0.0.1:42106 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:08.050 INFO [pid-5742] @chatnlu_infer.py:99 top5：160,165,35,211,3, cost: 0.02313828468322754
2025-05-10 09:54:09.901 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:54:09.903 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:54:09.904 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.853240966796875
INFO:     127.0.0.1:54908 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:11.340 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,213,150,1,125, cost: 0.026478052139282227
2025-05-10 09:54:12.167 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_i9ylwn4wukxx5ympozudkzji', 'type': 'function'}]
2025-05-10 09:54:12.167 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8271222114562988
2025-05-10 09:54:12.168 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:54914 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:13.976 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,213,125,1,150, cost: 0.03071427345275879
INFO:     127.0.0.1:54930 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:15.068 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,133,428,346, cost: 0.026750802993774414
INFO:     127.0.0.1:54946 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:18.722 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,125,150,1,2, cost: 0.026540279388427734
INFO:     127.0.0.1:32864 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:29.933 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,428,346,133, cost: 0.02424335479736328
INFO:     127.0.0.1:48768 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:33.556 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,125,150,1,2, cost: 0.0328371524810791
INFO:     127.0.0.1:48778 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:37.711 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,319, cost: 0.025885820388793945
INFO:     127.0.0.1:48790 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:41.371 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,428,102, cost: 0.025169849395751953
2025-05-10 09:54:42.258 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "牙医诊所"}', 'name': 'Go_POI'}, 'id': 'call_kdcmjeyng31wxj3digiao0h4', 'type': 'function'}]
2025-05-10 09:54:42.259 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8883945941925049
2025-05-10 09:54:42.259 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:牙医诊所
INFO:     127.0.0.1:35398 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:45.266 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,150, cost: 0.026592016220092773
INFO:     127.0.0.1:35414 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:48.756 INFO [pid-5742] @chatnlu_infer.py:99 top5：262,398,68,248,92, cost: 0.026366710662841797
2025-05-10 09:54:49.556 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Nav_Collections'}, 'id': 'call_g4opmmanc94et180o1dkl4ky', 'type': 'function'}]
2025-05-10 09:54:49.557 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8006401062011719
2025-05-10 09:54:49.557 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开导航收藏夹-无
INFO:     127.0.0.1:51536 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:51.615 INFO [pid-5742] @chatnlu_infer.py:99 top5：370,297,262,175,398, cost: 0.02660822868347168
2025-05-10 09:54:52.384 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Collect_Current_Location'}, 'id': 'call_9m5tilu68tk88zyz5cuog124', 'type': 'function'}]
2025-05-10 09:54:52.385 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7700023651123047
2025-05-10 09:54:52.385 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：收藏当前地址-无
INFO:     127.0.0.1:51552 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:52.812 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,277,422,250, cost: 0.02424168586730957
INFO:     127.0.0.1:51558 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:54.863 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,312,431,363,51, cost: 0.02601790428161621
2025-05-10 09:54:55.838 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Style": "山河图"}', 'name': 'Search_Music'}, 'id': 'call_zx5t401kkwechy9xd66m7ozk', 'type': 'function'}]
2025-05-10 09:54:55.839 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9750339984893799
2025-05-10 09:54:55.839 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲主题:山河图
INFO:     127.0.0.1:51566 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:54:58.163 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,312,363,103,51, cost: 0.02506422996520996
2025-05-10 09:54:59.201 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Age": "90年代"}', 'name': 'Search_Music'}, 'id': 'call_he0prfkaiaqrns4sqfxvelhx', 'type': 'function'}]
2025-05-10 09:54:59.202 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0383155345916748
2025-05-10 09:54:59.202 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲年代:90年代
INFO:     127.0.0.1:52292 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:00.637 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,363,431,312,51, cost: 0.026528358459472656
2025-05-10 09:55:01.620 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Age": "00年代"}', 'name': 'Search_Music'}, 'id': 'call_yaetutl443p9e2uim52lsdw2', 'type': 'function'}]
2025-05-10 09:55:01.621 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9836139678955078
2025-05-10 09:55:01.621 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲年代:00年代
INFO:     127.0.0.1:52300 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:02.000 INFO [pid-5742] @chatnlu_infer.py:99 top5：293,13,177,434,205, cost: 0.02617931365966797
2025-05-10 09:55:02.770 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Take_Photo'}, 'id': 'call_3jwmfu9rox8l95au7w1wcxqu', 'type': 'function'}]
2025-05-10 09:55:02.771 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7705028057098389
2025-05-10 09:55:02.772 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拍照-无
INFO:     127.0.0.1:52306 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:03.854 INFO [pid-5742] @chatnlu_infer.py:99 top5：13,6,342,199,246, cost: 0.023169517517089844
2025-05-10 09:55:04.528 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Record_Video'}, 'id': 'call_setmwo7jghdrg6jfe93vh1d8', 'type': 'function'}]
2025-05-10 09:55:04.529 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.6746048927307129
2025-05-10 09:55:04.529 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：开始录像-无
INFO:     127.0.0.1:52308 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:05.037 INFO [pid-5742] @chatnlu_infer.py:99 top5：181,86,142,168,400, cost: 0.02409958839416504
2025-05-10 09:55:05.798 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Simple_Broadcast'}, 'id': 'call_2l4t0c8bw221hgr9mzdb44sc', 'type': 'function'}]
2025-05-10 09:55:05.799 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7616069316864014
2025-05-10 09:55:05.799 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开简洁播报-无
INFO:     127.0.0.1:52322 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:06.573 INFO [pid-5742] @chatnlu_infer.py:99 top5：86,181,294,9,400, cost: 0.026421785354614258
2025-05-10 09:55:07.320 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Close_Simple_Broadcast'}, 'id': 'call_m3xnmlw71rkn1a7td7ta2skp', 'type': 'function'}]
2025-05-10 09:55:07.320 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7467770576477051
2025-05-10 09:55:07.320 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：关闭简洁播报-无
INFO:     127.0.0.1:52324 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:07.892 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,137,2, cost: 0.024219512939453125
2025-05-10 09:55:08.906 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "过桥米线店"}', 'name': 'Go_POI'}, 'id': 'call_olgdphrempytmjccvorlve5g', 'type': 'function'}]
2025-05-10 09:55:08.907 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0143086910247803
2025-05-10 09:55:08.907 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:过桥米线店
INFO:     127.0.0.1:53636 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:10.281 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,150,428, cost: 0.02431488037109375
INFO:     127.0.0.1:53642 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:17.666 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,150,1,428,2, cost: 0.024449825286865234
INFO:     127.0.0.1:53654 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:20.739 INFO [pid-5742] @chatnlu_infer.py:99 top5：150,3,85,149,2, cost: 0.024276256561279297
2025-05-10 09:55:25.841 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:55:25.841 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:55:25.841 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.102301836013794
INFO:     127.0.0.1:36928 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:25.857 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,59, cost: 0.015063762664794922
2025-05-10 09:55:26.841 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "加油站", "Target": "当前位置"}', 'name': 'Go_POI'}, 'id': 'call_e5z76a1sonah18chozq8fhix', 'type': 'function'}]
2025-05-10 09:55:26.842 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.984126091003418
2025-05-10 09:55:26.842 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:加油站,landmark:当前位置
INFO:     127.0.0.1:36944 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:27.765 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,428,432, cost: 0.023893117904663086
2025-05-10 09:55:28.537 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_7cev0aii0vsxkbs4e1y7xyef', 'type': 'function'}]
2025-05-10 09:55:28.537 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.772160530090332
2025-05-10 09:55:28.538 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:36956 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:31.867 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,431,150, cost: 0.017578840255737305
INFO:     127.0.0.1:58986 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:37.836 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,84, cost: 0.02605295181274414
2025-05-10 09:55:38.947 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "东安湖体育馆"}', 'name': 'Go_POI'}, 'id': 'call_7dey3bs0drhr4hbd98zboeij', 'type': 'function'}]
2025-05-10 09:55:38.948 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.111260175704956
2025-05-10 09:55:38.948 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:东安湖体育馆
INFO:     127.0.0.1:41134 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:39.078 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,150, cost: 0.0261075496673584
INFO:     127.0.0.1:41146 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:45.603 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,346,1, cost: 0.02688145637512207
INFO:     127.0.0.1:41154 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:49.017 INFO [pid-5742] @chatnlu_infer.py:99 top5：15,167,136,4,158, cost: 0.02627873420715332
2025-05-10 09:55:50.271 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Number": "23", "Position": "主驾"}', 'name': 'Set_Air_Condition_Temperature'}, 'id': 'call_t8syg92yf2vtdmoh2pbcxezd', 'type': 'function'}]
2025-05-10 09:55:50.272 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2541446685791016
2025-05-10 09:55:50.272 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置空调温度-number:23,位置:主驾
INFO:     127.0.0.1:39706 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:52.144 INFO [pid-5742] @chatnlu_infer.py:99 top5：15,167,136,4,158, cost: 0.031071186065673828
2025-05-10 09:55:53.395 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Number": "23", "Position": "副驾"}', 'name': 'Set_Air_Condition_Temperature'}, 'id': 'call_xv5gm1ecptk2gag1f9leayp5', 'type': 'function'}]
2025-05-10 09:55:53.396 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2511141300201416
2025-05-10 09:55:53.396 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置空调温度-number:23,位置:副驾
INFO:     127.0.0.1:39720 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:53.457 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,156,292,150, cost: 0.02973651885986328
INFO:     127.0.0.1:39732 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:55:59.279 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,137,8, cost: 0.0313105583190918
2025-05-10 09:56:00.314 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "成都市", "POI": "鲍鱼餐厅"}', 'name': 'Go_POI'}, 'id': 'call_g56b82jmyjxzgut2qvvnztl3', 'type': 'function'}]
2025-05-10 09:56:00.315 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.035482406616211
2025-05-10 09:56:00.315 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:成都市,POI:鲍鱼餐厅
INFO:     127.0.0.1:44660 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:00.416 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,35,160,304,50, cost: 0.03183603286743164
INFO:     127.0.0.1:44668 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:04.528 INFO [pid-5742] @chatnlu_infer.py:99 top5：183,35,160,88,61, cost: 0.025968551635742188
2025-05-10 09:56:05.486 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Direction": "吹窗"}', 'name': 'Set_Wind_Direction'}, 'id': 'call_72k1bv1hshq4mub0ivg3d0b9', 'type': 'function'}]
2025-05-10 09:56:05.487 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9583888053894043
2025-05-10 09:56:05.487 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置空调风向-位置:吹窗
INFO:     127.0.0.1:44674 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:07.510 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,319,428,360, cost: 0.01728367805480957
2025-05-10 09:56:08.418 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "汽车维修店"}', 'name': 'Go_POI'}, 'id': 'call_gurgy85m17d2fag5awv96179', 'type': 'function'}]
2025-05-10 09:56:08.418 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9076473712921143
2025-05-10 09:56:08.419 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:汽车维修店
INFO:     127.0.0.1:44680 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:10.387 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,3,59, cost: 0.02498626708984375
2025-05-10 09:56:11.378 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "修车店"}', 'name': 'Go_POI'}, 'id': 'call_dh8dmuo8kb6twambkw0sf6tt', 'type': 'function'}]
2025-05-10 09:56:11.379 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9921417236328125
2025-05-10 09:56:11.379 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:修车店
INFO:     127.0.0.1:42068 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:12.829 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,432,1,52,2, cost: 0.03820610046386719
2025-05-10 09:56:13.985 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Maintenance": "1"}', 'name': 'Check_Car_Condition'}, 'id': 'call_fdj6v0mxp69wthene1xyr8qg', 'type': 'function'}]
2025-05-10 09:56:13.986 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.156186580657959
2025-05-10 09:56:13.986 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：车况检查-Maintenance:1
INFO:     127.0.0.1:42078 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:14.079 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,102,428,137, cost: 0.024974346160888672
2025-05-10 09:56:15.337 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "售后", "Target": "当前位置", "index": "1"}', 'name': 'Go_POI'}, 'id': 'call_or9g2oyxjtgzjkb33pr0v8hd', 'type': 'function'}]
2025-05-10 09:56:15.338 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2592270374298096
2025-05-10 09:56:15.338 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:售后,landmark:当前位置,选项:1
INFO:     127.0.0.1:42090 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:17.397 INFO [pid-5742] @chatnlu_infer.py:99 top5：220,218,274,399,248, cost: 0.026039838790893555
2025-05-10 09:56:18.172 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Owner_Service'}, 'id': 'call_kadshnautrvv1qmz3t57ivek', 'type': 'function'}]
2025-05-10 09:56:18.173 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7757887840270996
2025-05-10 09:56:18.173 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开车主服务-无
INFO:     127.0.0.1:42100 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:19.278 INFO [pid-5742] @chatnlu_infer.py:99 top5：435,3,171,234,27, cost: 0.02640390396118164
2025-05-10 09:56:20.145 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:56:20.147 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:56:20.148 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8682982921600342
INFO:     127.0.0.1:52418 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:21.480 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,435,171,428,133, cost: 0.024660587310791016
2025-05-10 09:56:22.181 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_ulavw4udwr2rt7od9ecakukl', 'type': 'function'}]
2025-05-10 09:56:22.183 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7021353244781494
2025-05-10 09:56:22.183 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:52428 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:25.169 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,1,354,319, cost: 0.025763988494873047
INFO:     127.0.0.1:52440 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:27.413 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,214,405,172,313, cost: 0.0249483585357666
2025-05-10 09:56:28.382 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Emergency": "110"}', 'name': 'Call_Emergency'}, 'id': 'call_4ab7h2a15n0dth1z8gjqhfx5', 'type': 'function'}]
2025-05-10 09:56:28.383 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9691221714019775
2025-05-10 09:56:28.383 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打紧急电话-紧急电话:110
INFO:     127.0.0.1:52442 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:28.482 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,2,150, cost: 0.026088714599609375
2025-05-10 09:56:33.630 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:56:33.630 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:56:33.630 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.1477952003479
INFO:     127.0.0.1:54668 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:34.852 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,84, cost: 0.025658369064331055
2025-05-10 09:56:35.766 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "故宫博物院"}', 'name': 'Go_POI'}, 'id': 'call_0jjtux5xg4gr0dblhdoz0msn', 'type': 'function'}]
2025-05-10 09:56:35.767 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.914682149887085
2025-05-10 09:56:35.767 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:故宫博物院
INFO:     127.0.0.1:54674 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:36.134 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,150, cost: 0.025342226028442383
INFO:     127.0.0.1:54688 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:40.850 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,59,3, cost: 0.024129390716552734
2025-05-10 09:56:41.770 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "成都"}', 'name': 'Go_POI'}, 'id': 'call_9qelvg105r9judid0036geu4', 'type': 'function'}]
2025-05-10 09:56:41.771 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9206030368804932
2025-05-10 09:56:41.771 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:成都
INFO:     127.0.0.1:56340 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:41.922 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,354, cost: 0.03168845176696777
INFO:     127.0.0.1:56350 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:45.852 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,431,363, cost: 0.026126384735107422
2025-05-10 09:56:46.773 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "张杰"}', 'name': 'Search_Music'}, 'id': 'call_cpr4wypcc4dxk8szs1j7n18c', 'type': 'function'}]
2025-05-10 09:56:46.774 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9211685657501221
2025-05-10 09:56:46.774 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:张杰
INFO:     127.0.0.1:56362 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:47.156 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,354, cost: 0.027631044387817383
INFO:     127.0.0.1:56368 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:50.853 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,363,431, cost: 0.024987459182739258
2025-05-10 09:56:51.784 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "谢娜"}', 'name': 'Search_Music'}, 'id': 'call_gv1s426qakrhopsy9qjtk25q', 'type': 'function'}]
2025-05-10 09:56:51.785 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9323198795318604
2025-05-10 09:56:51.786 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:谢娜
INFO:     127.0.0.1:53594 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:52.009 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,428,1, cost: 0.026442289352416992
INFO:     127.0.0.1:53608 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:56.427 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,312,363, cost: 0.0314481258392334
2025-05-10 09:56:57.516 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "汪峰", "Name": "怒放的生命"}', 'name': 'Search_Music'}, 'id': 'call_v75d1sapj52pf49dlru2d60r', 'type': 'function'}]
2025-05-10 09:56:57.517 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0901854038238525
2025-05-10 09:56:57.518 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:汪峰,歌曲:怒放的生命
INFO:     127.0.0.1:53616 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:56:57.620 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,292,2,8, cost: 0.03104257583618164
INFO:     127.0.0.1:53620 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:01.568 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,3,59, cost: 0.028119564056396484
2025-05-10 09:57:02.490 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "长城"}', 'name': 'Go_POI'}, 'id': 'call_u6mbqmgucqre4mraxhahwnum', 'type': 'function'}]
2025-05-10 09:57:02.491 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9218862056732178
2025-05-10 09:57:02.491 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:长城
INFO:     127.0.0.1:45798 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:02.635 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,354, cost: 0.026216745376586914
INFO:     127.0.0.1:45802 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:07.315 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,8, cost: 0.02635931968688965
2025-05-10 09:57:08.368 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "火锅店", "Target": "北京"}', 'name': 'Go_POI'}, 'id': 'call_32hs81s3dmnfq429jsfp0m4i', 'type': 'function'}]
2025-05-10 09:57:08.369 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0543224811553955
2025-05-10 09:57:08.369 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:火锅店,landmark:北京
INFO:     127.0.0.1:45814 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:08.631 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,1,319,26, cost: 0.02855825424194336
INFO:     127.0.0.1:34886 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:11.408 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,3,26,402,200, cost: 0.025198936462402344
2025-05-10 09:57:12.642 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "fog": "起大雾", "location": "重庆"}', 'name': 'Query_Weather'}, 'id': 'call_dc92zcij2weiwsekmxtpew3l', 'type': 'function'}]
2025-05-10 09:57:12.642 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2341241836547852
2025-05-10 09:57:12.643 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:今天,fog:起大雾,city:重庆
INFO:     127.0.0.1:34902 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:12.735 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,1,319,26, cost: 0.017535686492919922
INFO:     127.0.0.1:34918 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:16.439 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,402,180,192,154, cost: 0.026320457458496094
2025-05-10 09:57:17.666 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "下周", "location": "重庆"}', 'name': 'Query_Weather'}, 'id': 'call_xuv7fjndekn9kqznu143thqt', 'type': 'function'}]
2025-05-10 09:57:17.667 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2273304462432861
2025-05-10 09:57:17.667 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:下周,city:重庆
INFO:     127.0.0.1:34934 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:17.759 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,194,26,50,354, cost: 0.017017602920532227
2025-05-10 09:57:20.575 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:57:20.577 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:57:20.578 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.8185460567474365
INFO:     127.0.0.1:34938 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:21.154 INFO [pid-5742] @chatnlu_infer.py:99 top5：259,197,150,407,60, cost: 0.012391090393066406
2025-05-10 09:57:22.189 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Area": "重庆", "Today": "今天"}', 'name': 'Search_News'}, 'id': 'call_i7p5coix0ryhgsl8shpu2t2d', 'type': 'function'}]
2025-05-10 09:57:22.190 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0356707572937012
2025-05-10 09:57:22.190 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：新闻搜索-新闻地区:重庆,实时:今天
INFO:     127.0.0.1:48860 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:22.235 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,2,428,1, cost: 0.012235403060913086
INFO:     127.0.0.1:48876 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:25.160 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,431,363, cost: 0.012209653854370117
2025-05-10 09:57:26.049 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "张杰"}', 'name': 'Search_Music'}, 'id': 'call_kcokt3vleivtc0zkguk2r5x8', 'type': 'function'}]
2025-05-10 09:57:26.049 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8889501094818115
2025-05-10 09:57:26.049 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:张杰
INFO:     127.0.0.1:48882 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:26.135 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,51,150,386,149, cost: 0.012077093124389648
2025-05-10 09:57:31.237 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:57:31.237 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:57:31.237 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.101971864700317
INFO:     127.0.0.1:48894 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:31.261 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,363,103,312, cost: 0.022945404052734375
2025-05-10 09:57:32.374 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"variety_show": "快乐大本营", "Name": "快乐大本营主题曲"}', 'name': 'Search_Music'}, 'id': 'call_xd73vur7hp6bgduxi8as85be', 'type': 'function'}]
2025-05-10 09:57:32.375 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1133801937103271
2025-05-10 09:57:32.375 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-综艺节目:快乐大本营,歌曲:快乐大本营主题曲
INFO:     127.0.0.1:51952 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:32.473 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,51,150,264,277, cost: 0.026022672653198242
2025-05-10 09:57:33.334 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "快乐大本营"}', 'name': 'Search_Radio'}, 'id': 'call_e3uwa17uqzwuld4g3jv8j425', 'type': 'function'}]
2025-05-10 09:57:33.335 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8614928722381592
2025-05-10 09:57:33.335 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-节目名称:快乐大本营
INFO:     127.0.0.1:51966 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:41.076 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,103,312,363, cost: 0.026313304901123047
2025-05-10 09:57:41.957 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "谢娜"}', 'name': 'Search_Music'}, 'id': 'call_j4vg9xb4pbuh45jfzpfa44kq', 'type': 'function'}]
2025-05-10 09:57:41.958 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8817143440246582
2025-05-10 09:57:41.959 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:谢娜
INFO:     127.0.0.1:49296 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:42.039 INFO [pid-5742] @chatnlu_infer.py:99 top5：49,51,264,265,72, cost: 0.026567935943603516
2025-05-10 09:57:42.714 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:57:42.716 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:57:42.716 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.6772980690002441
INFO:     127.0.0.1:49312 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:43.753 INFO [pid-5742] @chatnlu_infer.py:99 top5：51,49,142,264,72, cost: 0.02846240997314453
2025-05-10 09:57:44.826 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "中国音乐之声"}', 'name': 'Open_Radio_By_Name'}, 'id': 'call_1haxxc0s29jj3hu5gwg53jsy', 'type': 'function'}]
2025-05-10 09:57:44.827 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0737767219543457
2025-05-10 09:57:44.827 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开指定电台-电台名称:中国音乐之声
INFO:     127.0.0.1:49316 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:44.973 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,150,221, cost: 0.02762293815612793
2025-05-10 09:57:50.123 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:57:50.123 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:57:50.124 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.14993953704834
INFO:     127.0.0.1:49322 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:50.536 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,51,312,103, cost: 0.027437448501586914
2025-05-10 09:57:51.555 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "张杰"}', 'name': 'Search_Music'}, 'id': 'call_s51mfkht7pq98px3bkgg8hew', 'type': 'function'}]
2025-05-10 09:57:51.556 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.020268440246582
2025-05-10 09:57:51.556 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:张杰
INFO:     127.0.0.1:56628 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:51.876 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,354, cost: 0.026471614837646484
INFO:     127.0.0.1:56644 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:56.575 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,363,3, cost: 0.02549576759338379
2025-05-10 09:57:57.773 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "张杰", "charts": "成名曲"}', 'name': 'Search_Music'}, 'id': 'call_uf7kkk4o5z1nl31ozq10ixc7', 'type': 'function'}]
2025-05-10 09:57:57.773 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.198385238647461
2025-05-10 09:57:57.774 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:张杰,榜单:成名曲
INFO:     127.0.0.1:56658 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:57:57.870 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,51,85, cost: 0.025870800018310547
2025-05-10 09:58:03.022 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:58:03.022 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:58:03.022 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.151400804519653
INFO:     127.0.0.1:48228 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:03.056 INFO [pid-5742] @chatnlu_infer.py:99 top5：51,2,49,386,142, cost: 0.024837493896484375
2025-05-10 09:58:04.247 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Person": "郭德纲的儿子"}', 'name': 'Search_Radio'}, 'id': 'call_h64gyay5dxqw2kl04i4u64wg', 'type': 'function'}]
2025-05-10 09:58:04.248 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1910176277160645
2025-05-10 09:58:04.248 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-主播名称:郭德纲的儿子
INFO:     127.0.0.1:48240 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:04.306 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,51,125, cost: 0.026474714279174805
2025-05-10 09:58:09.457 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:58:09.458 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:58:09.458 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.151977062225342
INFO:     127.0.0.1:48244 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:09.485 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,49,150,125, cost: 0.02558159828186035
2025-05-10 09:58:10.653 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Key": "于谦和王玥波的小品"}', 'name': 'Search_Radio'}, 'id': 'call_21glcm29g7upe2yhnj6v2e6p', 'type': 'function'}]
2025-05-10 09:58:10.653 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1683220863342285
2025-05-10 09:58:10.654 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-电台关键词:于谦和王玥波的小品
INFO:     127.0.0.1:36872 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:10.760 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,2,1,150, cost: 0.032651662826538086
INFO:     127.0.0.1:36874 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:14.595 INFO [pid-5742] @chatnlu_infer.py:99 top5：150,149,3,85,323, cost: 0.025449275970458984
2025-05-10 09:58:15.633 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Person": "唐纳德·特朗普", "Today": "1"}', 'name': 'Search_News'}, 'id': 'call_vzm11uebx27n299m02pne1kh', 'type': 'function'}]
2025-05-10 09:58:15.634 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0380923748016357
2025-05-10 09:58:15.634 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：新闻搜索-新闻人物:唐纳德·特朗普,实时:1
INFO:     127.0.0.1:36876 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:15.926 INFO [pid-5742] @chatnlu_infer.py:99 top5：199,3,293,155,13, cost: 0.026995182037353516
2025-05-10 09:58:17.993 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:58:17.995 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:58:17.996 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.0696961879730225
INFO:     127.0.0.1:36880 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:20.358 INFO [pid-5742] @chatnlu_infer.py:99 top5：199,92,155,243,13, cost: 0.025090694427490234
2025-05-10 09:58:21.115 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_DashCam'}, 'id': 'call_y8rl6yo5fkhgn41wk7i5oakb', 'type': 'function'}]
2025-05-10 09:58:21.116 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7585263252258301
2025-05-10 09:58:21.117 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开行车记录仪-无
INFO:     127.0.0.1:51462 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:21.631 INFO [pid-5742] @chatnlu_infer.py:99 top5：277,427,130,433,196, cost: 0.018595457077026367
2025-05-10 09:58:26.778 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:58:26.778 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:58:26.778 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.146697044372559
INFO:     127.0.0.1:51472 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:27.930 INFO [pid-5742] @chatnlu_infer.py:99 top5：196,130,125,218,142, cost: 0.026515483856201172
2025-05-10 09:58:28.791 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"app": "QQ音乐"}', 'name': 'Open_App'}, 'id': 'call_6atmnxmbxzjwitlnlutaimr6', 'type': 'function'}]
2025-05-10 09:58:28.792 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8616876602172852
2025-05-10 09:58:28.792 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开应用-非多媒体应用:QQ音乐
INFO:     127.0.0.1:46604 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:28.949 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,133,52,428, cost: 0.0262296199798584
INFO:     127.0.0.1:46618 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:34.228 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,40,196,130,274, cost: 0.026499509811401367
INFO:     127.0.0.1:46624 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:35.364 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,125,196, cost: 0.029055118560791016
INFO:     127.0.0.1:46628 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:40.653 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,3,137,59, cost: 0.026101112365722656
2025-05-10 09:58:41.729 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "星巴克", "Target": "当前位置"}', 'name': 'Go_POI'}, 'id': 'call_ta3an83ke3ak3ctixyojbt48', 'type': 'function'}]
2025-05-10 09:58:41.730 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0762019157409668
2025-05-10 09:58:41.730 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:星巴克,landmark:当前位置
INFO:     127.0.0.1:57984 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:41.830 INFO [pid-5742] @chatnlu_infer.py:99 top5：214,3,383,324,313, cost: 0.02603316307067871
2025-05-10 09:58:45.567 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:58:45.569 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:58:45.570 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.7390737533569336
INFO:     127.0.0.1:57990 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:47.220 INFO [pid-5742] @chatnlu_infer.py:99 top5：405,3,354,313,172, cost: 0.024863243103027344
2025-05-10 09:58:47.931 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Call_Phone'}, 'id': 'call_1boojwtbgil5o2mxljhrr25v', 'type': 'function'}]
2025-05-10 09:58:47.932 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7112369537353516
2025-05-10 09:58:47.932 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打电话-无
INFO:     127.0.0.1:58000 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:58:48.418 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,1,72, cost: 0.019127845764160156
2025-05-10 09:58:52.504 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:58:52.505 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:58:52.506 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.088040590286255
INFO:     127.0.0.1:42932 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:01.617 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,363,431, cost: 0.025403738021850586
2025-05-10 09:59:02.559 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "黄石"}', 'name': 'Search_Radio'}, 'id': 'call_nal5jn555cchgotldx9rzid3', 'type': 'function'}]
2025-05-10 09:59:02.560 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9432666301727295
2025-05-10 09:59:02.561 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-节目名称:黄石
INFO:     127.0.0.1:38770 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:02.621 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,313,387,247,200, cost: 0.025851726531982422
2025-05-10 09:59:07.752 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:59:07.753 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:59:07.753 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.131280422210693
INFO:     127.0.0.1:38778 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:08.579 INFO [pid-5742] @chatnlu_infer.py:99 top5：214,3,313,263,31, cost: 0.02436661720275879
2025-05-10 09:59:09.662 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Emergency": "110"}', 'name': 'Call_Emergency'}, 'id': 'call_4i5bk2r0vrrn10ar8t2xgf9u', 'type': 'function'}]
2025-05-10 09:59:09.663 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0831224918365479
2025-05-10 09:59:09.663 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打紧急电话-紧急电话:110
INFO:     127.0.0.1:43186 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:09.723 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,432,354,1,428, cost: 0.026787519454956055
INFO:     127.0.0.1:43194 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:14.322 INFO [pid-5742] @chatnlu_infer.py:99 top5：432,3,292,435,1, cost: 0.02483057975769043
2025-05-10 09:59:15.419 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Tire": "1"}', 'name': 'Check_Car_Condition'}, 'id': 'call_nqnm4s0y1grjbl030doursk1', 'type': 'function'}]
2025-05-10 09:59:15.420 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.097074270248413
2025-05-10 09:59:15.420 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：车况检查-tire:1
INFO:     127.0.0.1:43204 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:15.482 INFO [pid-5742] @chatnlu_infer.py:99 top5：15,136,120,4,167, cost: 0.026202678680419922
2025-05-10 09:59:17.024 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 09:59:17.027 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:59:17.028 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.5453479290008545
INFO:     127.0.0.1:43206 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:19.128 INFO [pid-5742] @chatnlu_infer.py:99 top5：15,136,4,167,162, cost: 0.025842905044555664
2025-05-10 09:59:20.258 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Number": "23"}', 'name': 'Set_Air_Condition_Temperature'}, 'id': 'call_p27r2vtq5tfzzd412hm82nsk', 'type': 'function'}]
2025-05-10 09:59:20.259 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1308479309082031
2025-05-10 09:59:20.259 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置空调温度-number:23
INFO:     127.0.0.1:34080 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:20.354 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,213,432,292, cost: 0.01807856559753418
2025-05-10 09:59:21.105 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_e9nxr2koi24p1skhhbufc4qd', 'type': 'function'}]
2025-05-10 09:59:21.106 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.751420259475708
2025-05-10 09:59:21.106 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:34094 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:24.696 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,213,3,194,137, cost: 0.025953292846679688
2025-05-10 09:59:25.685 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "能晒太阳的地方"}', 'name': 'Go_POI'}, 'id': 'call_voxg79ad3kebmg0a1jdfw8b5', 'type': 'function'}]
2025-05-10 09:59:25.686 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.989727258682251
2025-05-10 09:59:25.687 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:能晒太阳的地方
INFO:     127.0.0.1:34098 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:28.074 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,51,150,72, cost: 0.025398731231689453
2025-05-10 09:59:33.223 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:59:33.223 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:59:33.224 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.149500131607056
INFO:     127.0.0.1:45876 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:37.344 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,431,312, cost: 0.025586843490600586
2025-05-10 09:59:38.453 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "玛瑞卡·福斯索"}', 'name': 'Search_Music'}, 'id': 'call_bdujm55je5bd8idrn0uw078l', 'type': 'function'}]
2025-05-10 09:59:38.454 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1096580028533936
2025-05-10 09:59:38.454 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:玛瑞卡·福斯索
INFO:     127.0.0.1:45892 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:38.511 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,51,85, cost: 0.025615930557250977
INFO:     127.0.0.1:40640 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:43.379 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,363,312,103, cost: 0.026510953903198242
2025-05-10 09:59:44.331 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"TV": "射雕英雄传"}', 'name': 'Search_Music'}, 'id': 'call_2abhchog6v218fb481odtc9v', 'type': 'function'}]
2025-05-10 09:59:44.332 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9524729251861572
2025-05-10 09:59:44.332 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-电视剧:射雕英雄传
INFO:     127.0.0.1:40650 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:44.474 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,1,319,346, cost: 0.026195287704467773
INFO:     127.0.0.1:40664 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:46.993 INFO [pid-5742] @chatnlu_infer.py:99 top5：85,49,72,142,51, cost: 0.02647113800048828
2025-05-10 09:59:47.885 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "北京"}', 'name': 'Search_Radio'}, 'id': 'call_f4lh70rzwpfkb67s4dmb2bsl', 'type': 'function'}]
2025-05-10 09:59:47.885 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8920388221740723
2025-05-10 09:59:47.885 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-节目名称:北京
INFO:     127.0.0.1:40666 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:48.196 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,221,51,103, cost: 0.025537967681884766
2025-05-10 09:59:53.341 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 09:59:53.342 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 09:59:53.342 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.145312786102295
INFO:     127.0.0.1:52758 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:53.372 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,363,312,431,51, cost: 0.029033899307250977
2025-05-10 09:59:54.403 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"genre": "蓝调"}', 'name': 'Search_Music'}, 'id': 'call_a7jjj9d1r8qsoqy39shywbty', 'type': 'function'}]
2025-05-10 09:59:54.404 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0313191413879395
2025-05-10 09:59:54.404 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲流派:蓝调
INFO:     127.0.0.1:52774 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:54.505 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,51,221,208, cost: 0.026735782623291016
2025-05-10 09:59:55.357 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_drqfwemjon5a10lpw9cisrkn', 'type': 'function'}]
2025-05-10 09:59:55.358 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8529226779937744
2025-05-10 09:59:55.358 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:52782 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:58.633 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,44,51,113, cost: 0.025259017944335938
2025-05-10 09:59:59.660 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"exclude": "爵士", "genre": "蓝调"}', 'name': 'Search_Music'}, 'id': 'call_ljinxlm92qemfxou03ve7yka', 'type': 'function'}]
2025-05-10 09:59:59.661 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.027703046798706
2025-05-10 09:59:59.661 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-排除歌曲:爵士,歌曲流派:蓝调
INFO:     127.0.0.1:37296 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 09:59:59.825 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,103,317, cost: 0.026750564575195312
2025-05-10 10:00:04.971 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 10:00:04.972 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:00:04.972 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.146786451339722
INFO:     127.0.0.1:37306 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:05.978 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,51,3,312, cost: 0.027022838592529297
2025-05-10 10:00:07.199 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "Russell Hitchcock", "album": "Air Supply"}', 'name': 'Search_Music'}, 'id': 'call_no5zg6bkuyjhbajcs0ph3iob', 'type': 'function'}]
2025-05-10 10:00:07.200 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2213213443756104
2025-05-10 10:00:07.200 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:Russell Hitchcock,专辑:Air Supply
INFO:     127.0.0.1:37314 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:07.298 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,150,2,51,354, cost: 0.026933908462524414
INFO:     127.0.0.1:37318 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:11.690 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,59, cost: 0.026198863983154297
2025-05-10 10:00:12.710 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "复旦大学"}', 'name': 'Go_POI'}, 'id': 'call_cgwgifizgtkgfm7mtkrg3lad', 'type': 'function'}]
2025-05-10 10:00:12.711 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0209028720855713
2025-05-10 10:00:12.711 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:复旦大学
INFO:     127.0.0.1:52682 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:12.814 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,137,22, cost: 0.025382518768310547
2025-05-10 10:00:16.320 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:00:16.323 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:00:16.323 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.50931453704834
INFO:     127.0.0.1:52696 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:17.335 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,3,137,8, cost: 0.026801109313964844
2025-05-10 10:00:18.480 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "上海", "POI": "五角场街道25号"}', 'name': 'Go_POI'}, 'id': 'call_md2x6f8b90anjflknwezptus', 'type': 'function'}]
2025-05-10 10:00:18.480 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1452889442443848
2025-05-10 10:00:18.480 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:上海,POI:五角场街道25号
INFO:     127.0.0.1:52708 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:18.582 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,150,1,2,428, cost: 0.026574134826660156
INFO:     127.0.0.1:43516 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:21.444 INFO [pid-5742] @chatnlu_infer.py:99 top5：31,350,214,424,172, cost: 0.02558588981628418
2025-05-10 10:00:22.483 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Phone_Number": "610000"}', 'name': 'Call_Number'}, 'id': 'call_51qlensgkw509iqz0vr2pqsv', 'type': 'function'}]
2025-05-10 10:00:22.484 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0391767024993896
2025-05-10 10:00:22.484 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打指定电话-联系电话:610000
INFO:     127.0.0.1:43528 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:22.546 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,137,22, cost: 0.025725841522216797
2025-05-10 10:00:24.138 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:00:24.141 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:00:24.141 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.5950303077697754
INFO:     127.0.0.1:43542 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:28.468 INFO [pid-5742] @chatnlu_infer.py:99 top5：60,210,1,3,137, cost: 0.028520822525024414
2025-05-10 10:00:29.921 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:00:29.923 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:00:29.924 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.4559662342071533
INFO:     127.0.0.1:55536 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:29.981 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,430,150, cost: 0.02586054801940918
INFO:     127.0.0.1:55548 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:33.590 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,51,3, cost: 0.02999424934387207
2025-05-10 10:00:34.612 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Mood": "治愈"}', 'name': 'Search_Music'}, 'id': 'call_jcekcdcwdn8rs9srl47rzplc', 'type': 'function'}]
2025-05-10 10:00:34.612 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.022158145904541
2025-05-10 10:00:34.613 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲心情:治愈
INFO:     127.0.0.1:55552 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:34.694 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,277,150,2,427, cost: 0.024314165115356445
2025-05-10 10:00:35.720 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"charts": "新歌"}', 'name': 'Search_Music'}, 'id': 'call_015krp3muprks0ru73y3hzj0', 'type': 'function'}]
2025-05-10 10:00:35.721 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0266098976135254
2025-05-10 10:00:35.721 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-榜单:新歌
INFO:     127.0.0.1:55560 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:37.444 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,363,51, cost: 0.02547740936279297
2025-05-10 10:00:38.400 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"charts": "最近新歌榜单"}', 'name': 'Search_Music'}, 'id': 'call_zgpg0wgt2qw8diyy7vb02bc4', 'type': 'function'}]
2025-05-10 10:00:38.401 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9562463760375977
2025-05-10 10:00:38.401 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-榜单:最近新歌榜单
INFO:     127.0.0.1:55566 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:38.859 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,150,2,85,149, cost: 0.0256807804107666
2025-05-10 10:00:40.486 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:00:40.488 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:00:40.489 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.6299407482147217
INFO:     127.0.0.1:54090 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:47.523 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,51,363,312, cost: 0.015485286712646484
2025-05-10 10:00:48.664 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "张韶涵", "Singer2": "范玮琪"}', 'name': 'Search_Music'}, 'id': 'call_cakrsnxslzcetj85pe2uia8f', 'type': 'function'}]
2025-05-10 10:00:48.667 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1439950466156006
2025-05-10 10:00:48.667 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:张韶涵,歌手2:范玮琪
INFO:     127.0.0.1:54104 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:48.763 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,8,1,208, cost: 0.018013477325439453
INFO:     127.0.0.1:40836 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:55.529 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,178,103,317, cost: 0.014056682586669922
2025-05-10 10:00:56.401 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "懦夫"}', 'name': 'Search_Music'}, 'id': 'call_olpo2knhbzrk0p3l8rmbsekh', 'type': 'function'}]
2025-05-10 10:00:56.402 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8719284534454346
2025-05-10 10:00:56.402 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:懦夫
INFO:     127.0.0.1:40844 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:00:56.567 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,51,208,8, cost: 0.016443729400634766
2025-05-10 10:00:57.645 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"album": "", "Singer": "周杰伦"}', 'name': 'Search_Music'}, 'id': 'call_zwzm9ch7kurwa2p3x16qfoxt', 'type': 'function'}]
2025-05-10 10:00:57.646 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0778493881225586
2025-05-10 10:00:57.646 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:周杰伦
INFO:     127.0.0.1:40860 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:01.647 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,198,2,312,219, cost: 0.015976667404174805
2025-05-10 10:01:02.694 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Media_Source": "专辑"}', 'name': 'Media_Collect'}, 'id': 'call_ld4kcdqmpk1z5nmnei0ev3c2', 'type': 'function'}]
2025-05-10 10:01:02.694 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.047569990158081
2025-05-10 10:01:02.695 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：收藏-媒体源:专辑
INFO:     127.0.0.1:59776 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:02.947 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,150, cost: 0.015820026397705078
INFO:     127.0.0.1:59782 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:07.973 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,312,363,51, cost: 0.01553654670715332
2025-05-10 10:01:09.200 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "周杰伦", "Singer2": "费玉清"}', 'name': 'Search_Music'}, 'id': 'call_aizznjzr9u2pflnn3t33tvgl', 'type': 'function'}]
2025-05-10 10:01:09.200 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2272758483886719
2025-05-10 10:01:09.200 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:周杰伦,歌手2:费玉清
INFO:     127.0.0.1:39776 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:09.289 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,428,102,137, cost: 0.01229405403137207
2025-05-10 10:01:09.853 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:01:09.855 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:01:09.856 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.5666377544403076
INFO:     127.0.0.1:39782 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:12.288 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,22, cost: 0.01572728157043457
2025-05-10 10:01:13.227 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "峨眉山脚下停车场"}', 'name': 'Go_POI'}, 'id': 'call_m1j7zjbd1m662116f8myjwb8', 'type': 'function'}]
2025-05-10 10:01:13.227 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9394075870513916
2025-05-10 10:01:13.228 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:峨眉山脚下停车场
INFO:     127.0.0.1:39788 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:13.404 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,2,125, cost: 0.017011404037475586
2025-05-10 10:01:18.553 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 10:01:18.553 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:01:18.553 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.149002313613892
INFO:     127.0.0.1:39794 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:20.495 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,8, cost: 0.026451587677001953
2025-05-10 10:01:21.667 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "杭州", "POI": "西湖", "Target": "星巴克"}', 'name': 'Go_POI'}, 'id': 'call_w0zzt5ecp1txakj8rztz6bvr', 'type': 'function'}]
2025-05-10 10:01:21.668 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1734232902526855
2025-05-10 10:01:21.668 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:杭州,POI:西湖,landmark:星巴克
INFO:     127.0.0.1:59204 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:21.768 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,347,2,277,8, cost: 0.025974512100219727
2025-05-10 10:01:22.909 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:01:22.911 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:01:22.912 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1431913375854492
INFO:     127.0.0.1:59216 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:27.630 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,363,312, cost: 0.024908065795898438
2025-05-10 10:01:28.897 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"album": "Six Degrees", "Name": "圣诞星", "Singer": "周杰伦"}', 'name': 'Search_Music'}, 'id': 'call_vw3qit3dqy54j8refxpts1sh', 'type': 'function'}]
2025-05-10 10:01:28.898 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.266958236694336
2025-05-10 10:01:28.898 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-专辑:Six Degrees,歌曲:圣诞星,歌手:周杰伦
INFO:     127.0.0.1:59228 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:28.999 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,51,125, cost: 0.026279687881469727
INFO:     127.0.0.1:59510 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:34.034 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,312,51,317, cost: 0.026261091232299805
2025-05-10 10:01:34.581 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:01:34.583 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:01:34.583 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.5492599010467529
INFO:     127.0.0.1:59526 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:35.190 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,213,319,63, cost: 0.01861095428466797
2025-05-10 10:01:36.302 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:01:36.305 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:01:36.306 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1151483058929443
INFO:     127.0.0.1:59542 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:39.673 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,59, cost: 0.027250051498413086
2025-05-10 10:01:40.777 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "马里亚纳海沟"}', 'name': 'Go_POI'}, 'id': 'call_e1lmw12i725iekjn6fhi93j7', 'type': 'function'}]
2025-05-10 10:01:40.778 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1048896312713623
2025-05-10 10:01:40.778 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:马里亚纳海沟
INFO:     127.0.0.1:41502 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:40.853 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,199,13,293,171, cost: 0.0172274112701416
2025-05-10 10:01:42.875 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:01:42.877 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:01:42.877 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.023865222930908
INFO:     127.0.0.1:41514 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:46.153 INFO [pid-5742] @chatnlu_infer.py:99 top5：13,199,6,342,243, cost: 0.02607274055480957
2025-05-10 10:01:46.874 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Record_Video'}, 'id': 'call_9i1uxbk7wqlb6ejefd8hd9yo', 'type': 'function'}]
2025-05-10 10:01:46.875 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7218799591064453
2025-05-10 10:01:46.875 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：开始录像-无
INFO:     127.0.0.1:41526 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:47.199 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,35,304,62,96, cost: 0.02603316307067871
INFO:     127.0.0.1:41542 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:51.830 INFO [pid-5742] @chatnlu_infer.py:99 top5：35,62,160,61,304, cost: 0.025421619415283203
2025-05-10 10:01:52.733 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Position": ""}', 'name': 'Open_Window'}, 'id': 'call_d6mci0fdc2bpwgdgea884ovw', 'type': 'function'}]
2025-05-10 10:01:52.734 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9040884971618652
2025-05-10 10:01:52.734 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开车窗-无
INFO:     127.0.0.1:51908 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:52.905 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,293,428, cost: 0.025270938873291016
INFO:     127.0.0.1:51912 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:56.813 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,2,137,102, cost: 0.025061607360839844
2025-05-10 10:01:57.825 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "咖啡店"}', 'name': 'Go_POI'}, 'id': 'call_5wk4clvtufr6r397m6nxpvuw', 'type': 'function'}]
2025-05-10 10:01:57.826 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.013002872467041
2025-05-10 10:01:57.826 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:咖啡店
INFO:     127.0.0.1:51916 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:01:59.891 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,346, cost: 0.025039196014404297
INFO:     127.0.0.1:52468 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:05.187 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,3,102,59, cost: 0.024927616119384766
2025-05-10 10:02:06.105 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "奶茶店"}', 'name': 'Go_POI'}, 'id': 'call_9cpyixawi7bi8urokb3urlcn', 'type': 'function'}]
2025-05-10 10:02:06.106 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9185950756072998
2025-05-10 10:02:06.106 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:奶茶店
INFO:     127.0.0.1:52474 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:06.629 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,150, cost: 0.02647995948791504
INFO:     127.0.0.1:52484 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:10.027 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,3,59, cost: 0.025840044021606445
2025-05-10 10:02:10.956 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "故宫"}', 'name': 'Go_POI'}, 'id': 'call_pf8azyouk32c57o4gipt9hzb', 'type': 'function'}]
2025-05-10 10:02:10.957 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9303784370422363
2025-05-10 10:02:10.957 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:故宫
INFO:     127.0.0.1:47694 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:11.230 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,354, cost: 0.026165246963500977
INFO:     127.0.0.1:47700 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:14.498 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,431,51,215, cost: 0.025422334671020508
2025-05-10 10:02:16.004 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:02:16.006 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:02:16.007 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.5081732273101807
INFO:     127.0.0.1:47710 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:20.176 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,150,51, cost: 0.02601337432861328
INFO:     127.0.0.1:42376 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:26.283 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,22,84, cost: 0.02631831169128418
2025-05-10 10:02:27.874 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "北京", "POI": "故宫"}', 'name': 'Go_POI'}, 'id': 'call_ydt7bmrsb4ggw8c65yokujdn', 'type': 'function'}]
2025-05-10 10:02:27.875 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.5920968055725098
2025-05-10 10:02:27.876 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:北京,POI:故宫
INFO:     127.0.0.1:42382 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:27.927 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,150,2,1,428, cost: 0.020997285842895508
INFO:     127.0.0.1:49356 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:35.593 INFO [pid-5742] @chatnlu_infer.py:99 top5：129,216,289,172,3, cost: 0.026182174682617188
2025-05-10 10:02:36.537 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Wake": "习近平"}', 'name': 'Set_Wakeup_Words'}, 'id': 'call_jqb5yoxhcod4ot4lw9czdjag', 'type': 'function'}]
2025-05-10 10:02:36.538 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9444928169250488
2025-05-10 10:02:36.538 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：修改为指定唤醒词-唤醒词:习近平
INFO:     127.0.0.1:49368 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:36.941 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,430, cost: 0.025575876235961914
INFO:     127.0.0.1:49370 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:40.615 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,363,51, cost: 0.02581334114074707
2025-05-10 10:02:41.763 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Keywords": "白色花"}', 'name': 'Search_Music'}, 'id': 'call_3fr3ff3lj5mu17ls7p2hqdw1', 'type': 'function'}]
2025-05-10 10:02:41.764 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.149299144744873
2025-05-10 10:02:41.765 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲关键字:白色花
INFO:     127.0.0.1:39902 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:42.096 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,293, cost: 0.024722814559936523
INFO:     127.0.0.1:39912 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:47.418 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,431,103,363, cost: 0.025255680084228516
2025-05-10 10:02:48.385 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "小虎队"}', 'name': 'Search_Music'}, 'id': 'call_svdexks5fv0ufkm9jox96ya8', 'type': 'function'}]
2025-05-10 10:02:48.386 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.967404842376709
2025-05-10 10:02:48.386 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:小虎队
INFO:     127.0.0.1:39924 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:48.540 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,431,178,317, cost: 0.026188135147094727
2025-05-10 10:02:49.459 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"TV": "神雕侠侣"}', 'name': 'Search_Music'}, 'id': 'call_o3q8wigqnl4k6jooes2u39az', 'type': 'function'}]
2025-05-10 10:02:49.460 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9201552867889404
2025-05-10 10:02:49.460 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-电视剧:神雕侠侣
INFO:     127.0.0.1:51274 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:54.619 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,312,103,3, cost: 0.024588584899902344
2025-05-10 10:02:55.544 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"TV": "神雕侠侣"}', 'name': 'Search_Music'}, 'id': 'call_h6ztv6k9c5kmbamy3mf0eyqh', 'type': 'function'}]
2025-05-10 10:02:55.545 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9254727363586426
2025-05-10 10:02:55.545 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-电视剧:神雕侠侣
INFO:     127.0.0.1:51282 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:55.802 INFO [pid-5742] @chatnlu_infer.py:99 top5：253,249,20,212,286, cost: 0.027089595794677734
2025-05-10 10:02:59.227 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:02:59.229 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:02:59.230 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.4270029067993164
INFO:     127.0.0.1:51296 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:02:59.256 INFO [pid-5742] @chatnlu_infer.py:99 top5：286,3,58,337,166, cost: 0.024694204330444336
2025-05-10 10:03:00.329 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"speed": "1"}', 'name': 'Set_Speech_By_Speed'}, 'id': 'call_v091q2n001b2i8xgjw55lhhj', 'type': 'function'}]
2025-05-10 10:03:00.330 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.074052333831787
2025-05-10 10:03:00.330 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置语速-语速:1
INFO:     127.0.0.1:46438 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:00.429 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,319, cost: 0.025408267974853516
INFO:     127.0.0.1:46442 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:05.190 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,102,213, cost: 0.024523258209228516
2025-05-10 10:03:06.280 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "家", "Via": "便利店"}', 'name': 'Add_Via'}, 'id': 'call_lsysb2cvxcgff7k6jwmq961e', 'type': 'function'}]
2025-05-10 10:03:06.281 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0904936790466309
2025-05-10 10:03:06.281 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：添加途经点-POI:家,途经点1:便利店
INFO:     127.0.0.1:46452 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:06.712 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,431,178, cost: 0.02582573890686035
2025-05-10 10:03:07.411 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:03:07.413 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:03:07.414 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7014896869659424
INFO:     127.0.0.1:46458 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:12.377 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,103,363,312, cost: 0.026412010192871094
2025-05-10 10:03:13.591 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "王菲", "Singer2": "李健"}', 'name': 'Search_Music'}, 'id': 'call_tt9efksmocyt1367r205eifx', 'type': 'function'}]
2025-05-10 10:03:13.592 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.214454174041748
2025-05-10 10:03:13.592 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:王菲,歌手2:李健
INFO:     127.0.0.1:57498 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:13.690 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,354,346,428, cost: 0.024644851684570312
INFO:     127.0.0.1:57510 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:15.548 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,138,8,2,1, cost: 0.025542020797729492
INFO:     127.0.0.1:57520 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:16.808 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,63,24,11, cost: 0.025379180908203125
2025-05-10 10:03:17.615 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:03:17.617 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:03:17.618 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8089842796325684
INFO:     127.0.0.1:57534 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:18.699 INFO [pid-5742] @chatnlu_infer.py:99 top5：58,24,11,158,55, cost: 0.02673053741455078
2025-05-10 10:03:19.883 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Ratio": "0.1", "Sound_Source": "所有"}', 'name': 'Set_Sound_Volume'}, 'id': 'call_vxk1l20vgp3tyn257wdggrzj', 'type': 'function'}]
2025-05-10 10:03:19.884 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1845662593841553
2025-05-10 10:03:19.885 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置音量-ratio:0.1,音源:所有
INFO:     127.0.0.1:40160 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:19.954 INFO [pid-5742] @chatnlu_infer.py:99 top5：181,86,72,212,265, cost: 0.028172731399536133
2025-05-10 10:03:24.681 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:03:24.683 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:03:24.684 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.729272127151489
INFO:     127.0.0.1:40164 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:26.679 INFO [pid-5742] @chatnlu_infer.py:99 top5：55,168,58,75,72, cost: 0.026570558547973633
2025-05-10 10:03:27.768 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Sound_Source": "导航"}', 'name': 'Set_Sound_Volume_Max'}, 'id': 'call_yih3eem3ier7q1lmh2hd97bl', 'type': 'function'}]
2025-05-10 10:03:27.770 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0902152061462402
2025-05-10 10:03:27.770 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：声音调到最大-音源:导航
INFO:     127.0.0.1:40174 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:27.825 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,148,359,293,163, cost: 0.026439189910888672
2025-05-10 10:03:28.549 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Unknown'}, 'id': 'call_jo6kr8vdo5ddvxqbl0v5iugi', 'type': 'function'}]
2025-05-10 10:03:28.550 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.7243874073028564
2025-05-10 10:03:28.550 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：未知-无
INFO:     127.0.0.1:37522 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:32.028 INFO [pid-5742] @chatnlu_infer.py:99 top5：359,109,148,378,185, cost: 0.025999069213867188
2025-05-10 10:03:32.890 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Close_Electronic_Eye'}, 'id': 'call_enjtteen2sm7h164f53h24dv', 'type': 'function'}]
2025-05-10 10:03:32.891 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8633058071136475
2025-05-10 10:03:32.891 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：关闭电子眼-无
INFO:     127.0.0.1:37526 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:33.164 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,150, cost: 0.017276287078857422
INFO:     127.0.0.1:37542 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:37.150 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,102,59, cost: 0.026386022567749023
2025-05-10 10:03:38.081 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "麻将馆"}', 'name': 'Go_POI'}, 'id': 'call_19dp0eldc6isjvagzwm2vand', 'type': 'function'}]
2025-05-10 10:03:38.082 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9319448471069336
2025-05-10 10:03:38.082 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:麻将馆
INFO:     127.0.0.1:37546 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:40.457 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,428,2,319, cost: 0.016959190368652344
INFO:     127.0.0.1:56328 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:43.788 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,3,59, cost: 0.02532196044921875
2025-05-10 10:03:44.695 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "故宫"}', 'name': 'Go_POI'}, 'id': 'call_sbe4l1eqc5uu0iakiik62pnp', 'type': 'function'}]
2025-05-10 10:03:44.696 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9073302745819092
2025-05-10 10:03:44.696 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:故宫
INFO:     127.0.0.1:56342 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:48.891 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,150,346, cost: 0.02657794952392578
INFO:     127.0.0.1:56028 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:51.657 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,312,51,363, cost: 0.02649545669555664
2025-05-10 10:03:53.007 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Age": "1979", "charts": "经典怀旧榜"}', 'name': 'Search_Music'}, 'id': 'call_esgl44hw3le6n8kyzbdqw7cc', 'type': 'function'}]
2025-05-10 10:03:53.008 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.3501853942871094
2025-05-10 10:03:53.008 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲年代:1979,榜单:经典怀旧榜
INFO:     127.0.0.1:56038 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:53.110 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,354,428,210, cost: 0.025777339935302734
INFO:     127.0.0.1:56054 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:56.284 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,59,167, cost: 0.024968862533569336
2025-05-10 10:03:57.261 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "北京", "POI": "北京"}', 'name': 'Go_POI'}, 'id': 'call_ilz3ky4cc2951j1q36najvo7', 'type': 'function'}]
2025-05-10 10:03:57.262 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9770040512084961
2025-05-10 10:03:57.262 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:北京,POI:北京
INFO:     127.0.0.1:56066 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:57.359 INFO [pid-5742] @chatnlu_infer.py:99 top5：180,376,402,194,120, cost: 0.025121688842773438
2025-05-10 10:03:58.362 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "春季", "location": "成都"}', 'name': 'Query_Body_Temperature'}, 'id': 'call_w5doffuyczb38eroj2kn3xuz', 'type': 'function'}]
2025-05-10 10:03:58.363 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0033490657806396
2025-05-10 10:03:58.363 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查询体感温度-date:春季,city:成都
INFO:     127.0.0.1:56070 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:03:59.425 INFO [pid-5742] @chatnlu_infer.py:99 top5：136,15,88,3,57, cost: 0.023739337921142578
2025-05-10 10:04:00.612 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Number": "15"}', 'name': 'Set_Air_Condition_Temperature'}, 'id': 'call_8avguv7czs7hiwt9tgh5jn18', 'type': 'function'}]
2025-05-10 10:04:00.612 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1873741149902344
2025-05-10 10:04:00.613 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置空调温度-number:15
INFO:     127.0.0.1:59818 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:00.712 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,431,433,277, cost: 0.025691986083984375
INFO:     127.0.0.1:59826 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:06.018 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,312,363,103, cost: 0.024499177932739258
2025-05-10 10:04:06.927 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "筷子兄弟"}', 'name': 'Search_Music'}, 'id': 'call_qlt2lqdk12oppdcaha6pldjt', 'type': 'function'}]
2025-05-10 10:04:06.928 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9101047515869141
2025-05-10 10:04:06.928 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:筷子兄弟
INFO:     127.0.0.1:59838 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:07.178 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,125,51,428, cost: 0.025537967681884766
INFO:     127.0.0.1:59844 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:12.330 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,22, cost: 0.03078603744506836
2025-05-10 10:04:13.355 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "上海", "POI": "外滩"}', 'name': 'Go_POI'}, 'id': 'call_zt6fv0a0noxnex56d9xwm2at', 'type': 'function'}]
2025-05-10 10:04:13.356 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.025561809539795
2025-05-10 10:04:13.356 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:上海,POI:外滩
INFO:     127.0.0.1:41238 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:13.438 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,194,150, cost: 0.02739429473876953
INFO:     127.0.0.1:41242 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:18.718 INFO [pid-5742] @chatnlu_infer.py:99 top5：194,1,209,232,192, cost: 0.026622295379638672
2025-05-10 10:04:20.079 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"date": "今天", "location": "杭州西湖"}', 'name': 'Query_Weather'}, 'id': 'call_ppokwel68p7xumu1i72ba1eh', 'type': 'function'}]
2025-05-10 10:04:20.080 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.3617339134216309
2025-05-10 10:04:20.080 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：天气查询-date:今天,city:杭州西湖
INFO:     127.0.0.1:46630 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:25.116 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,354,428,2,145, cost: 0.0272214412689209
INFO:     127.0.0.1:46642 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:27.221 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,346,40,1,292, cost: 0.02628636360168457
INFO:     127.0.0.1:46652 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:28.193 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,150,428, cost: 0.026816606521606445
2025-05-10 10:04:29.427 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "成都", "POI": "经典小吃"}', 'name': 'Go_POI'}, 'id': 'call_b6e5kzm3nwsbfpvea9dbrax4', 'type': 'function'}]
2025-05-10 10:04:29.428 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2340586185455322
2025-05-10 10:04:29.428 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:成都,POI:经典小吃
INFO:     127.0.0.1:42666 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:35.328 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,3,137,8, cost: 0.02661871910095215
2025-05-10 10:04:36.510 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "成都", "POI": "经典小吃店"}', 'name': 'Go_POI'}, 'id': 'call_ijufqkr6o9kxgtfnsnkeamwy', 'type': 'function'}]
2025-05-10 10:04:36.510 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.181962490081787
2025-05-10 10:04:36.511 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:成都,POI:经典小吃店
INFO:     127.0.0.1:42682 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:36.722 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,137,428, cost: 0.030080556869506836
2025-05-10 10:04:38.115 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:04:38.117 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:04:38.118 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.3964133262634277
INFO:     127.0.0.1:42690 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:41.096 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,3,137,8, cost: 0.030457735061645508
2025-05-10 10:04:42.066 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "苏州", "POI": "阳澄湖"}', 'name': 'Go_POI'}, 'id': 'call_shhdp44goxhql0zqwi0n2j5x', 'type': 'function'}]
2025-05-10 10:04:42.067 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9698517322540283
2025-05-10 10:04:42.067 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:苏州,POI:阳澄湖
INFO:     127.0.0.1:42880 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:42.720 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,103,312, cost: 0.02810049057006836
2025-05-10 10:04:46.053 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "周杰伦", "charts": "经典怀旧榜"}', 'name': 'Search_Music'}, 'id': 'call_kjg6ogbzvmqpsnubdg6rzyiu', 'type': 'function'}]
2025-05-10 10:04:46.054 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.3332862854003906
2025-05-10 10:04:46.054 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:周杰伦,榜单:经典怀旧榜
INFO:     127.0.0.1:42896 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:54.299 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,363,312,51, cost: 0.025882244110107422
2025-05-10 10:04:55.586 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Name": "晴天"}', 'name': 'Search_Music'}, 'id': 'call_h8emu7b76jce9p41cd53u0xm', 'type': 'function'}]
2025-05-10 10:04:55.587 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.2872350215911865
2025-05-10 10:04:55.587 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲:晴天
INFO:     127.0.0.1:45670 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:04:55.682 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,293,430, cost: 0.026089191436767578
INFO:     127.0.0.1:45672 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:01.380 INFO [pid-5742] @chatnlu_infer.py:99 top5：355,148,305,89,3, cost: 0.02607274055480957
2025-05-10 10:05:03.740 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'View_Small_Map'}, 'id': 'call_x5kxyp9wvvo624y6ef39rkph', 'type': 'function'}]
2025-05-10 10:05:03.741 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.360640525817871
2025-05-10 10:05:03.742 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：查看小地图-无
INFO:     127.0.0.1:35418 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:07.493 INFO [pid-5742] @chatnlu_infer.py:99 top5：324,411,172,214,31, cost: 0.02627110481262207
2025-05-10 10:05:08.055 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:05:08.058 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:05:08.058 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.5646817684173584
INFO:     127.0.0.1:35432 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:12.042 INFO [pid-5742] @chatnlu_infer.py:99 top5：324,214,411,172,31, cost: 0.02571558952331543
2025-05-10 10:05:13.078 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Yellow": "12315"}', 'name': 'Call_Yellow_Page'}, 'id': 'call_omr6gj241mcccjqdsrr3zxo1', 'type': 'function'}]
2025-05-10 10:05:13.079 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0367317199707031
2025-05-10 10:05:13.079 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：拨打黄页-黄页电话:12315
INFO:     127.0.0.1:39306 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:15.748 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,428,1,150, cost: 0.028316974639892578
INFO:     127.0.0.1:39318 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:23.397 INFO [pid-5742] @chatnlu_infer.py:99 top5：51,49,386,2,142, cost: 0.026607990264892578
2025-05-10 10:05:25.856 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Person": "郭德纲", "Type": "相声"}', 'name': 'Search_Radio'}, 'id': 'call_1ssad69vx8hx434thb7gm7cr', 'type': 'function'}]
2025-05-10 10:05:25.857 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:2.459224224090576
2025-05-10 10:05:25.857 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：电台搜索-主播名称:郭德纲,电台类型:相声
INFO:     127.0.0.1:46612 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:25.955 INFO [pid-5742] @chatnlu_infer.py:99 top5：72,264,3,265,142, cost: 0.026211261749267578
2025-05-10 10:05:31.106 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 10:05:31.106 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:05:31.107 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.150792360305786
INFO:     127.0.0.1:46616 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:31.865 INFO [pid-5742] @chatnlu_infer.py:99 top5：72,103,142,8,58, cost: 0.027420520782470703
2025-05-10 10:05:32.853 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"FM": "99.8"}', 'name': 'Play_Local_Radio'}, 'id': 'call_qnjw6uhz0bls5m8xp93xajnk', 'type': 'function'}]
2025-05-10 10:05:32.854 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9894521236419678
2025-05-10 10:05:32.854 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：播放本地电台-FM:99.8
INFO:     127.0.0.1:50110 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:33.468 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,102,428,137, cost: 0.026042938232421875
2025-05-10 10:05:42.376 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:05:42.378 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:05:42.379 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:8.911000967025757
INFO:     127.0.0.1:50126 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:43.831 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,3,137,22, cost: 0.025141000747680664
2025-05-10 10:05:53.346 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "德阳", "Via": "三星堆博物馆"}', 'name': 'Add_Via'}, 'id': 'call_zgsae9nke7d3ecxpnae9j00d', 'type': 'function'}]
2025-05-10 10:05:53.347 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:9.515445947647095
2025-05-10 10:05:53.347 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：添加途经点-POI:德阳,途经点1:三星堆博物馆
INFO:     127.0.0.1:56886 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:05:53.411 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,293, cost: 0.025141239166259766
INFO:     127.0.0.1:41580 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:14.221 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,431,150, cost: 0.025900840759277344
2025-05-10 10:06:17.964 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:06:17.966 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:06:17.967 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.7454957962036133
INFO:     127.0.0.1:59350 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:21.383 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,150,428, cost: 0.013303041458129883
2025-05-10 10:06:26.529 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 10:06:26.530 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:06:26.530 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.146923303604126
INFO:     127.0.0.1:45470 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:36.150 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,102,59, cost: 0.01291656494140625
2025-05-10 10:06:37.051 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "赖汤圆"}', 'name': 'Go_POI'}, 'id': 'call_z51ax3g6im5vknawpnwkl1zl', 'type': 'function'}]
2025-05-10 10:06:37.052 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9018344879150391
2025-05-10 10:06:37.052 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:赖汤圆
INFO:     127.0.0.1:37434 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:37.455 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,51,150,125, cost: 0.013246297836303711
2025-05-10 10:06:42.597 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: HTTPSConnectionPool(host='ark.cn-beijing.volces.com', port=443): Read timed out. (read timeout=5)
2025-05-10 10:06:42.598 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:06:42.598 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.14235782623291
INFO:     127.0.0.1:37440 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:43.727 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,431,312,51,363, cost: 0.026161670684814453
2025-05-10 10:06:44.765 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Singer": "林心如"}', 'name': 'Search_Music'}, 'id': 'call_r13k43v1ml7lbcbna7msj0rh', 'type': 'function'}]
2025-05-10 10:06:44.766 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0383057594299316
2025-05-10 10:06:44.766 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌手:林心如
INFO:     127.0.0.1:56660 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:44.850 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,1,428,150, cost: 0.012822389602661133
INFO:     127.0.0.1:56670 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:49.337 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,102,137,3,59, cost: 0.025809288024902344
2025-05-10 10:06:52.646 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "西岳华山"}', 'name': 'Go_POI'}, 'id': 'call_6bg6vxcdhzb3m87kngym7fct', 'type': 'function'}]
2025-05-10 10:06:52.647 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.3096439838409424
2025-05-10 10:06:52.647 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:西岳华山
INFO:     127.0.0.1:47182 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:52.744 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,346,428,354, cost: 0.026338815689086914
INFO:     127.0.0.1:47198 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:55.691 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,102,3,59, cost: 0.02607893943786621
2025-05-10 10:06:56.618 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "台湾岛"}', 'name': 'Go_POI'}, 'id': 'call_mg0o5dvuby7qoq2wpcnw72m7', 'type': 'function'}]
2025-05-10 10:06:56.619 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9281322956085205
2025-05-10 10:06:56.619 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:台湾岛
INFO:     127.0.0.1:47208 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:06:57.133 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,354,2,346, cost: 0.02626776695251465
INFO:     127.0.0.1:47212 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:01.434 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,3,431,51,103, cost: 0.026482820510864258
2025-05-10 10:07:02.579 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Age": "1949年10月1日"}', 'name': 'Search_Music'}, 'id': 'call_osyuioz94vix1nz45dy8fyma', 'type': 'function'}]
2025-05-10 10:07:02.580 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.1455161571502686
2025-05-10 10:07:02.580 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲年代:1949年10月1日
INFO:     127.0.0.1:56688 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:02.780 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,2,150,428,319, cost: 0.02513718605041504
INFO:     127.0.0.1:56700 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:09.152 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,51,3,103,363, cost: 0.025276899337768555
2025-05-10 10:07:10.251 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Content": "儿歌", "Keywords": "二十四节气"}', 'name': 'Search_Music'}, 'id': 'call_5hsn1bguqi2jsvxgtrc7cjmj', 'type': 'function'}]
2025-05-10 10:07:10.251 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0988364219665527
2025-05-10 10:07:10.251 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-儿童歌曲:儿歌,歌曲关键字:二十四节气
INFO:     127.0.0.1:38442 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:10.351 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,64,176,121,366, cost: 0.023797273635864258
2025-05-10 10:07:15.019 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:07:15.021 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:07:15.023 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.6717915534973145
INFO:     127.0.0.1:38454 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:15.050 INFO [pid-5742] @chatnlu_infer.py:99 top5：64,366,176,16,255, cost: 0.025849342346191406
2025-05-10 10:07:15.866 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{}', 'name': 'Open_Env_Light'}, 'id': 'call_sgikb9pl4eu154bkrvazt6df', 'type': 'function'}]
2025-05-10 10:07:15.867 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.8167521953582764
2025-05-10 10:07:15.867 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开氛围灯-无
INFO:     127.0.0.1:38466 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:15.931 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,293,56, cost: 0.028034448623657227
INFO:     127.0.0.1:38476 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:20.752 INFO [pid-5742] @chatnlu_infer.py:99 top5：56,121,63,123,30, cost: 0.02688145637512207
2025-05-10 10:07:21.818 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Color": "浅灰"}', 'name': 'Set_Env_Light_Color'}, 'id': 'call_y2mnvz3k7bxez7yyc9dyotmv', 'type': 'function'}]
2025-05-10 10:07:21.819 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0666999816894531
2025-05-10 10:07:21.819 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：设置氛围灯颜色-COLOR:浅灰
INFO:     127.0.0.1:45140 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:22.083 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,2,428,346, cost: 0.0252993106842041
INFO:     127.0.0.1:45152 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:25.940 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,319,1,35,360, cost: 0.02649402618408203
2025-05-10 10:07:26.921 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Position": "主驾"}', 'name': 'Open_Window'}, 'id': 'call_cczlcxe8efp1isscnzgsdalq', 'type': 'function'}]
2025-05-10 10:07:26.922 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.98199462890625
2025-05-10 10:07:26.922 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：打开车窗-位置:主驾
INFO:     127.0.0.1:45154 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:27.524 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,129,2,428,346, cost: 0.027148962020874023
INFO:     127.0.0.1:45156 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:30.377 INFO [pid-5742] @chatnlu_infer.py:99 top5：216,129,3,431,172, cost: 0.02752852439880371
2025-05-10 10:07:31.339 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"Wake": "美猴王"}', 'name': 'Set_Wakeup_Words'}, 'id': 'call_2hqsf8j63kxf8waylau401im', 'type': 'function'}]
2025-05-10 10:07:31.340 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9621241092681885
2025-05-10 10:07:31.340 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：修改为指定唤醒词-唤醒词:美猴王
INFO:     127.0.0.1:57238 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:31.816 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,3,363,51, cost: 0.026578187942504883
2025-05-10 10:07:36.173 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:07:36.175 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:07:36.176 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:4.359259605407715
INFO:     127.0.0.1:57254 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:45.615 INFO [pid-5742] @chatnlu_infer.py:99 top5：2,103,363,312,51, cost: 0.02622675895690918
2025-05-10 10:07:51.535 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"genre": "古典"}', 'name': 'Search_Music'}, 'id': 'call_3ly4pysakrvvu0n9swieo708', 'type': 'function'}]
2025-05-10 10:07:51.536 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:5.920335292816162
2025-05-10 10:07:51.536 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：音乐搜索-歌曲流派:古典
INFO:     127.0.0.1:42912 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:51.594 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,346,428,354, cost: 0.027560710906982422
INFO:     127.0.0.1:49034 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:55.942 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,102,428, cost: 0.0263974666595459
2025-05-10 10:07:56.970 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "中国最大的岛屿"}', 'name': 'Go_POI'}, 'id': 'call_bm1fg165pj64mvww5lsswdf5', 'type': 'function'}]
2025-05-10 10:07:56.971 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:1.0286366939544678
2025-05-10 10:07:56.971 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:中国最大的岛屿
INFO:     127.0.0.1:49044 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:07:58.303 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,1,354,60,346, cost: 0.025861740112304688
2025-05-10 10:08:02.014 ERROR [pid-5742] @chatnlu_infer.py:82 Doubao error: 'tool_calls'
2025-05-10 10:08:02.015 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：None
2025-05-10 10:08:02.016 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:3.712360382080078
INFO:     127.0.0.1:54230 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:08:05.779 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,3,137,428,102, cost: 0.025969743728637695
2025-05-10 10:08:06.688 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"City": "成都市"}', 'name': 'Go_POI'}, 'id': 'call_8j16z39mp5ivck72h4caybuz', 'type': 'function'}]
2025-05-10 10:08:06.689 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.9096114635467529
2025-05-10 10:08:06.690 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-city:成都市
INFO:     127.0.0.1:54232 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:08:09.177 INFO [pid-5742] @chatnlu_infer.py:99 top5：3,428,1,293,354, cost: 0.02545332908630371
INFO:     127.0.0.1:57664 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
2025-05-10 10:08:18.195 INFO [pid-5742] @chatnlu_infer.py:99 top5：1,137,59,102,288, cost: 0.026281356811523438
2025-05-10 10:08:19.107 INFO [pid-5742] @chatnlu_infer.py:118 llm结果：[{'function': {'arguments': '{"POI": "户籍所在地派出所"}', 'name': 'Go_POI'}, 'id': 'call_pu1pwnqe284qo92wkwnn0yfw', 'type': 'function'}]
2025-05-10 10:08:19.108 INFO [pid-5742] @chatnlu_infer.py:119 function调用时间:0.912647008895874
2025-05-10 10:08:19.108 INFO [pid-5742] @chatnlu_infer.py:127 返回结果：导航搜索-POI:户籍所在地派出所
INFO:     127.0.0.1:56398 - "POST /chatnlu-server/v1 HTTP/1.1" 200 OK
