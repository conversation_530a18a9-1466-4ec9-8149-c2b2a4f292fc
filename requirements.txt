accelerate==1.6.0
aiocache==0.12.3
aiohappyeyeballs==2.6.1
aiohttp==3.11.16
aiosignal==1.3.2
airportsdata==20250224
annotated-types==0.7.0
antlr4-python3-runtime==4.13.2
anyio==4.9.0
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
astor==0.8.1
asttokens==3.0.0
async-lru==2.0.5
attrs==25.3.0
babel==2.17.0
beautifulsoup4==4.13.4
bidict==0.23.1
blake3==1.0.4
bleach==6.2.0
blinker==1.9.0
boto3==1.38.2
botocore==1.38.2
Brotli==1.1.0
cachetools==5.5.2
certifi==2025.1.31
cffi==1.17.1
charset-normalizer==3.4.1
click==8.1.8
cloudpickle==3.1.1
comm==0.2.2
compressed-tensors==0.9.3
ConfigArgParse==1.7
cryptography==44.0.1
cupy-cuda12x==13.4.1
datasets==3.5.0
debugpy==1.8.14
decorator==5.2.1
defusedxml==0.7.1
Deprecated==1.2.18
depyf==0.18.0
dill==0.3.8
diskcache==5.6.3
distro==1.9.0
dnspython==2.7.0
docker-pycreds==0.4.0
einops==0.8.1
email_validator==2.2.0
executing==2.2.0
fastapi==0.115.12
fastapi-cli==0.0.7
fastjsonschema==2.21.1
fastrlock==0.8.3
filelock==3.18.0
Flask==3.1.0
flask-cors==5.0.1
Flask-Login==0.6.3
Flask-SocketIO==5.5.1
fqdn==1.5.1
frozenlist==1.6.0
fsspec==2024.12.0
gevent==24.11.1
geventhttpclient==2.3.3
gguf==0.16.0
gitdb==4.0.12
GitPython==3.1.44
googleapis-common-protos==1.70.0
greenlet==3.2.1
grpcio==1.71.0
h11==0.14.0
h2==4.2.0
hf-xet==1.0.3
hpack==4.1.0
httpcore==1.0.8
httptools==0.6.4
httpx==0.28.1
httpx-sse==0.4.0
huggingface-hub==0.30.2
hyperframe==6.1.0
idna==3.10
importlib_metadata==8.0.0
interegular==0.3.3
ipykernel==6.29.5
ipython==9.1.0
ipython_pygments_lexers==1.1.1
ipywidgets==8.1.6
isoduration==20.11.0
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.6
jiter==0.9.0
jmespath==1.0.1
joblib==1.4.2
json5==0.12.0
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter==1.1.1
jupyter-console==6.6.3
jupyter-events==0.12.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.15.0
jupyter_server_terminals==0.5.3
jupyterlab==4.4.0
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.14
lark==1.2.2
latex2sympy2_extended==1.10.1
llguidance==0.7.16
llvmlite==0.44.0
lm-format-enforcer==0.10.11
locust==2.36.2
locust-cloud==1.20.6
markdown-it-py==3.0.0
MarkupSafe==3.0.2
math-verify==0.7.0
matplotlib-inline==0.1.7
mcp==1.7.0
mdurl==0.1.2
mistral_common==1.5.4
mistune==3.1.3
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.19.0
multidict==6.4.3
multiprocess==0.70.16
nbclient==0.10.2
nbconvert==7.16.6
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.4.2
ninja==********
notebook==7.4.0
notebook_shim==0.2.4
numba==0.61.2
numpy==2.2.4
nvidia-cublas-cu12==********
nvidia-cuda-cupti-cu12==12.4.127
nvidia-cuda-nvrtc-cu12==12.4.127
nvidia-cuda-runtime-cu12==12.4.127
nvidia-cudnn-cu12==********
nvidia-cufft-cu12==********
nvidia-curand-cu12==**********
nvidia-cusolver-cu12==********
nvidia-cusparse-cu12==**********
nvidia-cusparselt-cu12==0.6.2
nvidia-nccl-cu12==2.21.5
nvidia-nvjitlink-cu12==12.4.127
nvidia-nvtx-cu12==12.4.127
openai==1.75.0
opencv-python-headless==*********
opentelemetry-api==1.26.0
opentelemetry-exporter-otlp==1.26.0
opentelemetry-exporter-otlp-proto-common==1.26.0
opentelemetry-exporter-otlp-proto-grpc==1.26.0
opentelemetry-exporter-otlp-proto-http==1.26.0
opentelemetry-proto==1.26.0
opentelemetry-sdk==1.26.0
opentelemetry-semantic-conventions==0.47b0
opentelemetry-semantic-conventions-ai==0.4.3
orjson==3.10.18
outlines==0.1.11
outlines_core==0.1.26
overrides==7.7.0
packaging==24.2
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
partial-json-parser==*******.post5
peft==0.15.2
pexpect==4.9.0
pillow==11.2.1
platformdirs==4.3.7
prometheus-fastapi-instrumentator==7.1.0
prometheus_client==0.21.1
prompt_toolkit==3.0.51
propcache==0.3.1
protobuf==4.25.6
psutil==7.0.0
ptyprocess==0.7.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyarrow==19.0.1
pycountry==24.6.1
pycparser==2.22
pydantic==2.11.3
pydantic-settings==2.9.1
pydantic_core==2.33.1
Pygments==2.19.1
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-engineio==4.12.0
python-json-logger==3.3.0
python-multipart==0.0.20
python-socketio==5.13.0
pytz==2025.2
PyYAML==6.0.2
pyzmq==26.4.0
qqmusic-api-python==0.3.4
ray==2.43.0
redis==5.2.1
referencing==0.36.2
regex==2024.11.6
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986-validator==0.1.1
rich==14.0.0
rich-toolkit==0.14.1
rpds-py==0.24.0
s3transfer==0.12.0
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.2
Send2Trash==1.8.3
sentencepiece==0.2.0
sentry-sdk==2.26.1
setproctitle==1.3.5
setuptools==75.8.0
shellingham==1.5.4
simple-websocket==1.1.0
sinan==0.1.5
six==1.17.0
smmap==5.0.2
sniffio==1.3.1
soupsieve==2.6
sse-starlette==2.3.3
stack-data==0.6.3
starlette==0.46.2
sympy==1.13.1
terminado==0.18.1
threadpoolctl==3.6.0
tiktoken==0.9.0
tinycss2==1.4.0
tokenizers==0.21.1
torch==2.6.0
torchaudio==2.6.0
torchvision==0.21.0
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.51.3
triton==3.2.0
trl==0.16.1
typer==0.15.2
types-python-dateutil==2.9.0.20241206
typing-inspection==0.4.0
typing_extensions==4.13.2
tzdata==2025.2
uri-template==1.3.0
urllib3==2.4.0
uvicorn==0.34.1
uvloop==0.21.0
wandb==0.19.9
watchfiles==1.0.5
wcwidth==0.2.13
webcolors==24.11.1
webencodings==0.5.1
websocket-client==1.8.0
websockets==15.0.1
Werkzeug==3.1.3
wheel==0.45.1
widgetsnbextension==4.0.14
wrapt==1.17.2
wsproto==1.2.0
xformers==0.0.29.post2
xgrammar==0.1.18
xxhash==3.5.0
yarl==1.20.0
zipp==3.21.0
zope.event==5.0
zope.interface==7.2
